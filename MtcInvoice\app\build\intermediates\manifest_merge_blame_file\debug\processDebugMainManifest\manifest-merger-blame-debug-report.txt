1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.official.invoicegenarator"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:5:5-67
11-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
12-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:6:5-81
12-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:6:22-78
13    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
13-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:7:5-9:40
13-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:8:9-66
14    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" /> <!-- Need this for API 33 -->
14-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:10:5-80
14-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:10:22-77
15    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
15-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:11:5-76
15-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:11:22-73
16    <uses-permission android:name="android.permission.USE_BIOMETRIC" /> <!-- suppress DeprecatedClassUsageInspection -->
16-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2a8aa2878027e87434341bd924ace03\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
16-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2a8aa2878027e87434341bd924ace03\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
17    <uses-permission android:name="android.permission.USE_FINGERPRINT" /> <!-- Although the *SdkVersion is captured in gradle build files, this is required for non gradle builds -->
17-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2a8aa2878027e87434341bd924ace03\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
17-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2a8aa2878027e87434341bd924ace03\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
18    <!-- <uses-sdk android:minSdkVersion="21"/> -->
19    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
19-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b765ec09f16a5ac6a0a5e66ab1f912bd\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:22:5-79
19-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b765ec09f16a5ac6a0a5e66ab1f912bd\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:22:22-76
20    <uses-permission android:name="android.permission.WAKE_LOCK" />
20-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9ab80f88383b7964e93a8ff0ee42f678\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:24:5-68
20-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9ab80f88383b7964e93a8ff0ee42f678\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:24:22-65
21    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
21-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9ab80f88383b7964e93a8ff0ee42f678\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:25:5-79
21-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9ab80f88383b7964e93a8ff0ee42f678\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:25:22-76
22    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
22-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9ab80f88383b7964e93a8ff0ee42f678\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:26:5-88
22-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9ab80f88383b7964e93a8ff0ee42f678\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:26:22-85
23    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
23-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9ab80f88383b7964e93a8ff0ee42f678\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:27:5-82
23-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9ab80f88383b7964e93a8ff0ee42f678\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:27:22-79
24    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
24-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f13e1d7ad41e179a13cf86061b25b951\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:26:5-110
24-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f13e1d7ad41e179a13cf86061b25b951\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:26:22-107
25
26    <permission
26-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0168c57cfd1a8d4ed3c839d0e62e1137\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
27        android:name="com.official.invoicegenarator.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
27-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0168c57cfd1a8d4ed3c839d0e62e1137\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
28        android:protectionLevel="signature" />
28-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0168c57cfd1a8d4ed3c839d0e62e1137\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
29
30    <uses-permission android:name="com.official.invoicegenarator.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
30-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0168c57cfd1a8d4ed3c839d0e62e1137\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
30-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0168c57cfd1a8d4ed3c839d0e62e1137\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
31
32    <application
32-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:13:5-64:19
33        android:allowBackup="true"
33-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:14:9-35
34        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
34-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0168c57cfd1a8d4ed3c839d0e62e1137\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
35        android:dataExtractionRules="@xml/data_extraction_rules"
35-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:15:9-65
36        android:debuggable="true"
37        android:extractNativeLibs="false"
38        android:fullBackupContent="@xml/backup_rules"
38-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:16:9-54
39        android:icon="@mipmap/ic_launcher"
39-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:17:9-43
40        android:label="@string/app_name"
40-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:18:9-41
41        android:networkSecurityConfig="@xml/network_security_config"
41-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:19:9-69
42        android:roundIcon="@mipmap/ic_launcher_round"
42-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:20:9-54
43        android:supportsRtl="true"
43-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:21:9-35
44        android:theme="@style/Theme.NewInvoice" >
44-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:22:9-48
45        <activity
45-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:24:9-26:40
46            android:name="com.official.invoicegenarator.InvoiceTwo"
46-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:25:13-39
47            android:exported="false" />
47-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:26:13-37
48        <activity
48-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:27:9-29:40
49            android:name="com.official.invoicegenarator.InvoiceTraker"
49-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:28:13-42
50            android:exported="false" />
50-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:29:13-37
51        <activity
51-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:30:9-32:40
52            android:name="com.official.invoicegenarator.PdfViewerActivity"
52-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:31:13-46
53            android:exported="false" />
53-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:32:13-37
54        <activity
54-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:33:9-35:40
55            android:name="com.official.invoicegenarator.DownloadListActivity"
55-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:34:13-49
56            android:exported="false" />
56-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:35:13-37
57        <activity
57-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:36:9-38:40
58            android:name="com.official.invoicegenarator.MoneyBagActivity"
58-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:37:13-45
59            android:exported="false" />
59-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:38:13-37
60        <activity
60-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:39:9-41:40
61            android:name="com.official.invoicegenarator.FingerprintSettingsActivity"
61-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:40:13-56
62            android:exported="false" />
62-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:41:13-37
63        <activity
63-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:42:9-44:40
64            android:name="com.official.invoicegenarator.VarifyActivity"
64-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:43:13-43
65            android:exported="false" />
65-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:44:13-37
66        <activity
66-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:45:9-47:40
67            android:name="com.official.invoicegenarator.WorkerAttendenceActivity"
67-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:46:13-53
68            android:exported="false" />
68-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:47:13-37
69        <activity
69-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:48:9-50:40
70            android:name="com.official.invoicegenarator.SelectionActivity"
70-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:49:13-46
71            android:exported="false" />
71-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:50:13-37
72        <activity
72-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:51:9-54:55
73            android:name="com.official.invoicegenarator.Home"
73-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:52:13-33
74            android:exported="false"
74-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:53:13-37
75            android:windowSoftInputMode="adjustPan" />
75-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:54:13-52
76        <activity
76-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:55:9-63:20
77            android:name="com.official.invoicegenarator.MainActivity"
77-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:56:13-41
78            android:exported="true" >
78-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:57:13-36
79            <intent-filter>
79-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:58:13-62:29
80                <action android:name="android.intent.action.MAIN" />
80-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:59:17-69
80-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:59:25-66
81
82                <category android:name="android.intent.category.LAUNCHER" />
82-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:61:17-77
82-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:61:27-74
83            </intent-filter>
84        </activity>
85        <activity
85-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\eb6dae60fd8fb9cee4fb7bcddf95903c\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:27:9-29:72
86            android:name="com.karumi.dexter.DexterActivity"
86-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\eb6dae60fd8fb9cee4fb7bcddf95903c\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:28:13-60
87            android:theme="@style/Dexter.Internal.Theme.Transparent" />
87-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\eb6dae60fd8fb9cee4fb7bcddf95903c\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:29:13-69
88
89        <service
89-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b765ec09f16a5ac6a0a5e66ab1f912bd\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:26:9-35:19
90            android:name="com.google.firebase.components.ComponentDiscoveryService"
90-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b765ec09f16a5ac6a0a5e66ab1f912bd\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:27:13-84
91            android:directBootAware="true"
91-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\690ba2f772e2f392c9bd32f81534ff20\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
92            android:exported="false" >
92-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b765ec09f16a5ac6a0a5e66ab1f912bd\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:28:13-37
93            <meta-data
93-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b765ec09f16a5ac6a0a5e66ab1f912bd\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
94                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
94-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b765ec09f16a5ac6a0a5e66ab1f912bd\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:30:17-120
95                android:value="com.google.firebase.components.ComponentRegistrar" />
95-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b765ec09f16a5ac6a0a5e66ab1f912bd\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:31:17-82
96            <meta-data
96-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b765ec09f16a5ac6a0a5e66ab1f912bd\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
97                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
97-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b765ec09f16a5ac6a0a5e66ab1f912bd\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:33:17-109
98                android:value="com.google.firebase.components.ComponentRegistrar" />
98-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b765ec09f16a5ac6a0a5e66ab1f912bd\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:34:17-82
99            <meta-data
99-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\905ae480009aa267311a7d8e01d981fc\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:30:13-32:85
100                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
100-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\905ae480009aa267311a7d8e01d981fc\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:31:17-118
101                android:value="com.google.firebase.components.ComponentRegistrar" />
101-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\905ae480009aa267311a7d8e01d981fc\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:32:17-82
102            <meta-data
102-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\905ae480009aa267311a7d8e01d981fc\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:33:13-35:85
103                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
103-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\905ae480009aa267311a7d8e01d981fc\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:34:17-107
104                android:value="com.google.firebase.components.ComponentRegistrar" />
104-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\905ae480009aa267311a7d8e01d981fc\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:35:17-82
105            <meta-data
105-->[com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dc1290f4c450a51cff82df63825afd23\transformed\jetified-firebase-analytics-ktx-22.1.2\AndroidManifest.xml:11:13-13:85
106                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar"
106-->[com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dc1290f4c450a51cff82df63825afd23\transformed\jetified-firebase-analytics-ktx-22.1.2\AndroidManifest.xml:12:17-129
107                android:value="com.google.firebase.components.ComponentRegistrar" />
107-->[com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dc1290f4c450a51cff82df63825afd23\transformed\jetified-firebase-analytics-ktx-22.1.2\AndroidManifest.xml:13:17-82
108            <meta-data
108-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b7895d036f18c6d1c36cc1ec53cdf476\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:25:13-27:85
109                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
109-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b7895d036f18c6d1c36cc1ec53cdf476\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:26:17-120
110                android:value="com.google.firebase.components.ComponentRegistrar" />
110-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b7895d036f18c6d1c36cc1ec53cdf476\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:27:17-82
111            <meta-data
111-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b7895d036f18c6d1c36cc1ec53cdf476\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:28:13-30:85
112                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
112-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b7895d036f18c6d1c36cc1ec53cdf476\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:29:17-117
113                android:value="com.google.firebase.components.ComponentRegistrar" />
113-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b7895d036f18c6d1c36cc1ec53cdf476\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:30:17-82
114            <meta-data
114-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9ab80f88383b7964e93a8ff0ee42f678\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:37:13-39:85
115                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
115-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9ab80f88383b7964e93a8ff0ee42f678\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:38:17-139
116                android:value="com.google.firebase.components.ComponentRegistrar" />
116-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9ab80f88383b7964e93a8ff0ee42f678\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:39:17-82
117            <meta-data
117-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ad12a4bb62f7a6d050071bef23bbb374\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
118                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
118-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ad12a4bb62f7a6d050071bef23bbb374\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
119                android:value="com.google.firebase.components.ComponentRegistrar" />
119-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ad12a4bb62f7a6d050071bef23bbb374\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
120            <meta-data
120-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ad12a4bb62f7a6d050071bef23bbb374\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
121                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
121-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ad12a4bb62f7a6d050071bef23bbb374\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
122                android:value="com.google.firebase.components.ComponentRegistrar" />
122-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ad12a4bb62f7a6d050071bef23bbb374\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
123            <meta-data
123-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\2667f3c10ff5005af88e6a4897006765\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
124                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
124-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\2667f3c10ff5005af88e6a4897006765\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
125                android:value="com.google.firebase.components.ComponentRegistrar" />
125-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\2667f3c10ff5005af88e6a4897006765\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
126            <meta-data
126-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\690ba2f772e2f392c9bd32f81534ff20\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
127                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
127-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\690ba2f772e2f392c9bd32f81534ff20\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
128                android:value="com.google.firebase.components.ComponentRegistrar" />
128-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\690ba2f772e2f392c9bd32f81534ff20\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
129        </service>
130
131        <property
131-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9ab80f88383b7964e93a8ff0ee42f678\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:30:9-32:61
132            android:name="android.adservices.AD_SERVICES_CONFIG"
132-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9ab80f88383b7964e93a8ff0ee42f678\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:31:13-65
133            android:resource="@xml/ga_ad_services_config" />
133-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9ab80f88383b7964e93a8ff0ee42f678\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:32:13-58
134
135        <provider
135-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\690ba2f772e2f392c9bd32f81534ff20\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
136            android:name="com.google.firebase.provider.FirebaseInitProvider"
136-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\690ba2f772e2f392c9bd32f81534ff20\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
137            android:authorities="com.official.invoicegenarator.firebaseinitprovider"
137-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\690ba2f772e2f392c9bd32f81534ff20\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
138            android:directBootAware="true"
138-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\690ba2f772e2f392c9bd32f81534ff20\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
139            android:exported="false"
139-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\690ba2f772e2f392c9bd32f81534ff20\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
140            android:initOrder="100" />
140-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\690ba2f772e2f392c9bd32f81534ff20\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
141
142        <receiver
142-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f13e1d7ad41e179a13cf86061b25b951\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:29:9-33:20
143            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
143-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f13e1d7ad41e179a13cf86061b25b951\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:30:13-85
144            android:enabled="true"
144-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f13e1d7ad41e179a13cf86061b25b951\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:31:13-35
145            android:exported="false" >
145-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f13e1d7ad41e179a13cf86061b25b951\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:32:13-37
146        </receiver>
147
148        <service
148-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f13e1d7ad41e179a13cf86061b25b951\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:35:9-38:40
149            android:name="com.google.android.gms.measurement.AppMeasurementService"
149-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f13e1d7ad41e179a13cf86061b25b951\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:36:13-84
150            android:enabled="true"
150-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f13e1d7ad41e179a13cf86061b25b951\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:37:13-35
151            android:exported="false" />
151-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f13e1d7ad41e179a13cf86061b25b951\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:38:13-37
152        <service
152-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f13e1d7ad41e179a13cf86061b25b951\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:39:9-43:72
153            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
153-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f13e1d7ad41e179a13cf86061b25b951\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:40:13-87
154            android:enabled="true"
154-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f13e1d7ad41e179a13cf86061b25b951\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:41:13-35
155            android:exported="false"
155-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f13e1d7ad41e179a13cf86061b25b951\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:42:13-37
156            android:permission="android.permission.BIND_JOB_SERVICE" />
156-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f13e1d7ad41e179a13cf86061b25b951\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:43:13-69
157
158        <activity
158-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\096f1df436aae94fbd41c57fb8e017a8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
159            android:name="com.google.android.gms.common.api.GoogleApiActivity"
159-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\096f1df436aae94fbd41c57fb8e017a8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
160            android:exported="false"
160-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\096f1df436aae94fbd41c57fb8e017a8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
161            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
161-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\096f1df436aae94fbd41c57fb8e017a8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
162
163        <uses-library
163-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8976dce55b28e13ef82c0d0ace60d70a\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
164            android:name="android.ext.adservices"
164-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8976dce55b28e13ef82c0d0ace60d70a\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
165            android:required="false" />
165-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8976dce55b28e13ef82c0d0ace60d70a\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
166
167        <provider
167-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8fa9f103640828fc63b2ba63b777d76f\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
168            android:name="androidx.startup.InitializationProvider"
168-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8fa9f103640828fc63b2ba63b777d76f\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
169            android:authorities="com.official.invoicegenarator.androidx-startup"
169-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8fa9f103640828fc63b2ba63b777d76f\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
170            android:exported="false" >
170-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8fa9f103640828fc63b2ba63b777d76f\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
171            <meta-data
171-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8fa9f103640828fc63b2ba63b777d76f\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
172                android:name="androidx.emoji2.text.EmojiCompatInitializer"
172-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8fa9f103640828fc63b2ba63b777d76f\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
173                android:value="androidx.startup" />
173-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8fa9f103640828fc63b2ba63b777d76f\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
174            <meta-data
174-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\162434658d2e3a566644b2cbbaabb9e7\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
175                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
175-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\162434658d2e3a566644b2cbbaabb9e7\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
176                android:value="androidx.startup" />
176-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\162434658d2e3a566644b2cbbaabb9e7\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
177            <meta-data
177-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c942f702031c0b4aaf5c9c8e584a2948\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
178                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
178-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c942f702031c0b4aaf5c9c8e584a2948\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
179                android:value="androidx.startup" />
179-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c942f702031c0b4aaf5c9c8e584a2948\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
180        </provider>
181
182        <meta-data
182-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\841427592dbdc5b54021fc73bff9e716\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
183            android:name="com.google.android.gms.version"
183-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\841427592dbdc5b54021fc73bff9e716\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
184            android:value="@integer/google_play_services_version" />
184-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\841427592dbdc5b54021fc73bff9e716\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
185
186        <receiver
186-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c942f702031c0b4aaf5c9c8e584a2948\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
187            android:name="androidx.profileinstaller.ProfileInstallReceiver"
187-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c942f702031c0b4aaf5c9c8e584a2948\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
188            android:directBootAware="false"
188-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c942f702031c0b4aaf5c9c8e584a2948\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
189            android:enabled="true"
189-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c942f702031c0b4aaf5c9c8e584a2948\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
190            android:exported="true"
190-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c942f702031c0b4aaf5c9c8e584a2948\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
191            android:permission="android.permission.DUMP" >
191-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c942f702031c0b4aaf5c9c8e584a2948\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
192            <intent-filter>
192-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c942f702031c0b4aaf5c9c8e584a2948\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
193                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
193-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c942f702031c0b4aaf5c9c8e584a2948\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
193-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c942f702031c0b4aaf5c9c8e584a2948\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
194            </intent-filter>
195            <intent-filter>
195-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c942f702031c0b4aaf5c9c8e584a2948\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
196                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
196-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c942f702031c0b4aaf5c9c8e584a2948\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
196-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c942f702031c0b4aaf5c9c8e584a2948\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
197            </intent-filter>
198            <intent-filter>
198-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c942f702031c0b4aaf5c9c8e584a2948\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
199                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
199-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c942f702031c0b4aaf5c9c8e584a2948\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
199-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c942f702031c0b4aaf5c9c8e584a2948\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
200            </intent-filter>
201            <intent-filter>
201-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c942f702031c0b4aaf5c9c8e584a2948\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
202                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
202-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c942f702031c0b4aaf5c9c8e584a2948\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
202-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c942f702031c0b4aaf5c9c8e584a2948\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
203            </intent-filter>
204        </receiver>
205    </application>
206
207</manifest>
