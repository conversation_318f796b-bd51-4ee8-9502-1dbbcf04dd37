package com.official.invoicegenarator;

import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.airbnb.lottie.LottieAnimationView;
import com.google.android.material.dialog.MaterialAlertDialogBuilder;
import com.official.invoicegenarator.network.ApiService;
import com.official.invoicegenarator.network.ApiCallback;
import com.official.invoicegenarator.models.DataItem;
import com.official.invoicegenarator.database.InvoiceDataHelper;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Locale;

// Using the shared DataItem model from models package

// Main Fragment class for updating and deleting invoice data
public class UpdateDeleteFragment extends Fragment {

    private RecyclerView recyclerView;
    private DataAdapter dataAdapter;
    private ArrayList<DataItem> dataList;
    private ApiService apiService;
    private InvoiceDataHelper dbHelper;
    private ProgressBar progressBar;
    private EditText searchBar;
    private LottieAnimationView lottieAnimationView;


    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_update_delete, container, false);

        // Initialize API service and database helper
        apiService = new ApiService();
        dbHelper = InvoiceDataHelper.getInstance(getContext());

        recyclerView = view.findViewById(R.id.recyclerView);
        recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        lottieAnimationView = view.findViewById(R.id.lottieAnimationView);
        searchBar = view.findViewById(R.id.search_bar);
        dataList = new ArrayList<>();
        dataAdapter = new DataAdapter(dataList, new DataAdapter.OnItemClickListener() {
            @Override
            public void onEditClick(DataItem item) {
                showUpdateDialog(item);
            }

            @Override
            public void onDeleteClick(String itemId) {
                showDeleteDialog(itemId);
            }
        });

        recyclerView.setAdapter(dataAdapter);
        loadData();

        searchBar.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                filterData(s.toString());
            }

            @Override
            public void afterTextChanged(Editable s) {}
        });
        return view;
    }

    private void loadData() {
        lottieAnimationView.setVisibility(View.VISIBLE); // Show Lottie animation

        // Load from local database first (offline-first approach)
        List<DataItem> localItems = dbHelper.getAllInvoiceItems();
        dataList.clear();

        // Format timestamps for display
        for (DataItem item : localItems) {
            if (item.getCurrentDateTime() == null || item.getCurrentDateTime().isEmpty()) {
                long timestamp = item.getTimestamp();
                SimpleDateFormat sdf = new SimpleDateFormat("hh:mm a, dd/MM/yyyy", Locale.getDefault());
                String dateAndTime = sdf.format(new Date(timestamp));
                item.setCurrentDateTime(dateAndTime);
            }
        }

        // Reverse the list to show newest first
        Collections.reverse(localItems);
        dataList.addAll(localItems);
        dataAdapter.notifyDataSetChanged();
        lottieAnimationView.setVisibility(View.GONE);

        // Then try to sync with server in background
        syncWithServer();
    }

    private void syncWithServer() {
        apiService.getInvoiceItems(new ApiCallback<List<DataItem>>() {
            @Override
            public void onSuccess(List<DataItem> serverItems) {
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        // Update local database with server data
                        for (DataItem item : serverItems) {
                            item.setSynced(true);
                            DataItem existingItem = dbHelper.getInvoiceItemById(item.getId());
                            if (existingItem != null) {
                                dbHelper.updateInvoiceItem(item);
                            } else {
                                dbHelper.insertInvoiceItem(item);
                            }
                        }

                        // Refresh UI with updated data
                        loadLocalData();
                    });
                }
            }

            @Override
            public void onError(String error) {
                // Continue with local data, no need to show error to user
                // as we already have local data displayed
            }
        });
    }

    private void loadLocalData() {
        List<DataItem> localItems = dbHelper.getAllInvoiceItems();
        dataList.clear();

        // Format timestamps for display
        for (DataItem item : localItems) {
            if (item.getCurrentDateTime() == null || item.getCurrentDateTime().isEmpty()) {
                long timestamp = item.getTimestamp();
                SimpleDateFormat sdf = new SimpleDateFormat("hh:mm a, dd/MM/yyyy", Locale.getDefault());
                String dateAndTime = sdf.format(new Date(timestamp));
                item.setCurrentDateTime(dateAndTime);
            }
        }

        Collections.reverse(localItems);
        dataList.addAll(localItems);
        dataAdapter.notifyDataSetChanged();
    }


    private void filterData(String query) {
        ArrayList<DataItem> filteredList = new ArrayList<>();

        // Check if the query is empty
        if (query.isEmpty()) {
            // If empty, show all items
            filteredList.addAll(dataList);
        } else {
            // Otherwise, filter based on the query
            for (DataItem item : dataList) {
                if (item.getDescription().toLowerCase().contains(query.toLowerCase()) ||
                        item.getLocation().toLowerCase().contains(query.toLowerCase()) ||
                        item.getQr().toLowerCase().contains(query.toLowerCase()) ||
                        item.getLpo().toLowerCase().contains(query.toLowerCase()) ||
                        item.getInb().toLowerCase().contains(query.toLowerCase()) ||
                        item.getCurrentDateTime().toLowerCase().contains(query.toLowerCase()) ||
                        item.getAmount().toLowerCase().contains(query.toLowerCase()) ||
                        item.getPaymentStatus().toLowerCase().contains(query.toLowerCase()) ||
                        item.getW_a().toLowerCase().contains(query.toLowerCase())) {
                    filteredList.add(item);
                }
            }
        }

        // Update the adapter with the filtered list
        dataAdapter.filterList(filteredList);
    }

    private void showUpdateDialog(DataItem item) {
        MaterialAlertDialogBuilder builder = new MaterialAlertDialogBuilder(getContext());
        builder.setTitle("Update Item");

        View dialogView = LayoutInflater.from(getContext()).inflate(R.layout.dialog_update_item, null);
        builder.setView(dialogView);

        EditText editTextDescription = dialogView.findViewById(R.id.editTextDescription);
        EditText editTextLocation = dialogView.findViewById(R.id.editTextLocation);
        EditText editTextQr = dialogView.findViewById(R.id.editTextQr);
        EditText editTextLpo = dialogView.findViewById(R.id.editTextLpo);
        EditText editTextInb = dialogView.findViewById(R.id.editTextInb);
        EditText editTextAmount = dialogView.findViewById(R.id.editTextAmount);
        EditText editTextW_a = dialogView.findViewById(R.id.editTextW_a);
        EditText editTextPaymentStatus = dialogView.findViewById(R.id.payment_status_update);

        // Set existing values in the dialog fields
        editTextDescription.setText(item.getDescription());
        editTextLocation.setText(item.getLocation());
        editTextQr.setText(item.getQr());
        editTextLpo.setText(item.getLpo());
        editTextInb.setText(item.getInb());
        editTextAmount.setText(item.getAmount());
        editTextW_a.setText(item.getW_a());
        editTextPaymentStatus.setText(item.getPaymentStatus());

        builder.setPositiveButton("Update", (dialog, which) -> {
            String updatedDescription = editTextDescription.getText().toString();
            String updatedLocation = editTextLocation.getText().toString();
            String updatedQr = editTextQr.getText().toString();
            String updatedLpo = editTextLpo.getText().toString();
            String updatedInb = editTextInb.getText().toString();
            String updatedAmount = editTextAmount.getText().toString();
            String updatedW_a = editTextW_a.getText().toString();
            String updatedPaymentStatus = editTextPaymentStatus.getText().toString();

            // Get the current timestamp
            long currentTimestamp = System.currentTimeMillis();
            SimpleDateFormat sdf = new SimpleDateFormat("hh:mm a, dd/MM/yyyy", Locale.getDefault());
            String currentDateTime = sdf.format(new Date(currentTimestamp));

            // Create an updated item with the new values and timestamp
            DataItem updatedItem = new DataItem(
                    updatedDescription, updatedLocation, updatedQr, updatedLpo, updatedInb,
                    updatedAmount, updatedW_a, updatedPaymentStatus, currentTimestamp
            );
            updatedItem.setId(item.getId());
            updatedItem.setCurrentDateTime(currentDateTime);

            // Update locally first
            dbHelper.updateInvoiceItem(updatedItem);

            // Try to sync with server
            apiService.updateInvoiceItem(item.getId(), updatedItem, new ApiCallback<DataItem>() {
                @Override
                public void onSuccess(DataItem result) {
                    result.setSynced(true);
                    dbHelper.updateInvoiceItem(result);
                    if (getActivity() != null) {
                        getActivity().runOnUiThread(() -> {
                            Toast.makeText(getContext(), "Item updated successfully", Toast.LENGTH_SHORT).show();
                            loadLocalData();
                        });
                    }
                }

                @Override
                public void onError(String error) {
                    if (getActivity() != null) {
                        getActivity().runOnUiThread(() -> {
                            Toast.makeText(getContext(), "Updated locally, will sync when online", Toast.LENGTH_SHORT).show();
                            loadLocalData();
                        });
                    }
                }
            });
        });

        builder.setNegativeButton("Cancel", (dialog, which) -> dialog.dismiss());
        builder.show();
    }


    private void showDeleteDialog(String itemId) {
        MaterialAlertDialogBuilder builder = new MaterialAlertDialogBuilder(getContext());
        builder.setTitle("Delete Item")
                .setMessage("Are you sure you want to delete this item?")
                .setPositiveButton("Delete", (dialog, which) -> deleteItem(itemId))
                .setNegativeButton("Cancel", (dialog, which) -> dialog.dismiss())
                .show();
    }

    private void deleteItem(String itemId) {
        // Soft delete locally first
        dbHelper.deleteInvoiceItem(itemId);

        // Try to delete on server
        apiService.deleteInvoiceItem(itemId, new ApiCallback<Void>() {
            @Override
            public void onSuccess(Void result) {
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        Toast.makeText(getContext(), "Item deleted successfully", Toast.LENGTH_SHORT).show();
                        loadLocalData();
                    });
                }
            }

            @Override
            public void onError(String error) {
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        Toast.makeText(getContext(), "Deleted locally, will sync when online", Toast.LENGTH_SHORT).show();
                        loadLocalData();
                    });
                }
            }
        });
    }


}

// Adapter class to bind invoice data to the RecyclerView
class DataAdapter extends RecyclerView.Adapter<DataAdapter.ViewHolder> {
    private ArrayList<DataItem> dataList;
    private OnItemClickListener listener;

    public interface OnItemClickListener {
        void onEditClick(DataItem item);
        void onDeleteClick(String itemId);
    }

    public DataAdapter(ArrayList<DataItem> dataList, OnItemClickListener listener) {
        this.dataList = dataList;
        this.listener = listener;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_data_invoice_traker, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        DataItem item = dataList.get(position);

        holder.textViewDescription.setText("Description: " + item.getDescription());
        holder.textViewLocation.setText("Location: " + item.getLocation());
        holder.textViewQr.setText("QR Code: " + item.getQr());
        holder.textViewLpo.setText("LPO: " + item.getLpo());
        holder.textViewInb.setText("Invoice Number: " + item.getInb());
        holder.textViewAmount.setText("Amount: " + item.getAmount());
        holder.textViewW_a.setText("W/A: " + item.getW_a());
        holder.textViewPaymentStatus.setText("Payment Status: " + item.getPaymentStatus());

        holder.textTimeDate.setText("Time and Date:\n" + item.getCurrentDateTime());

        holder.buttonEdit.setOnClickListener(v -> listener.onEditClick(item));
        holder.buttonDelete.setOnClickListener(v -> listener.onDeleteClick(item.getId()));
    }

    @Override
    public int getItemCount() {
        return dataList.size();
    }

    public void filterList(ArrayList<DataItem> filteredList) {
        this.dataList = filteredList;
        notifyDataSetChanged();
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        TextView textViewDescription, textViewLocation, textViewQr, textViewLpo, textViewInb, textViewAmount, textViewW_a, textViewPaymentStatus, textTimeDate;
        Button buttonEdit, buttonDelete;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            textViewDescription = itemView.findViewById(R.id.text_description);
            textViewLocation = itemView.findViewById(R.id.text_location);
            textViewQr = itemView.findViewById(R.id.text_qr);
            textViewLpo = itemView.findViewById(R.id.text_lpo);
            textViewInb = itemView.findViewById(R.id.text_inb);
            textViewAmount = itemView.findViewById(R.id.text_amount);
            textViewW_a = itemView.findViewById(R.id.text_w_a);
            textViewPaymentStatus = itemView.findViewById(R.id.text_payment_status);
            textTimeDate = itemView.findViewById(R.id.text_time_date); // TextView for date and time
            buttonEdit = itemView.findViewById(R.id.button_edit);
            buttonDelete = itemView.findViewById(R.id.button_delete);
        }
    }
}
