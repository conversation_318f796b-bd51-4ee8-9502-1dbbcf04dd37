# Android Migration Dependencies

## Required Dependencies for Firebase Migration

Add these dependencies to your `app/build.gradle` file:

```gradle
dependencies {
    // Existing dependencies...
    
    // HTTP Client for API communication (replaces Firebase)
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'
    
    // JSON parsing (already present in your project)
    // implementation 'com.google.code.gson:gson:2.10.1'
    
    // Optional: For better network monitoring
    implementation 'com.squareup.okhttp3:okhttp-urlconnection:4.12.0'
}
```

## Permissions

Add these permissions to your `AndroidManifest.xml` if not already present:

```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
```

## ProGuard Rules

If you're using ProGuard/R8, add these rules to your `proguard-rules.pro`:

```proguard
# OkHttp
-dontwarn okhttp3.**
-dontwarn okio.**
-dontwarn javax.annotation.**
-keepnames class okhttp3.internal.publicsuffix.PublicSuffixDatabase

# Gson
-keepattributes Signature
-keepattributes *Annotation*
-dontwarn sun.misc.**
-keep class com.google.gson.** { *; }
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

# Keep model classes
-keep class com.official.invoicegenarator.models.** { *; }
-keep class com.official.invoicegenarator.network.** { *; }
```

## Network Security Config

For HTTP connections (if your API is not HTTPS), add this to your `AndroidManifest.xml`:

```xml
<application
    android:networkSecurityConfig="@xml/network_security_config"
    ... >
```

And create `res/xml/network_security_config.xml`:

```xml
<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <domain-config cleartextTrafficPermitted="true">
        <domain includeSubdomains="true">*************</domain>
        <domain includeSubdomains="true">localhost</domain>
        <domain includeSubdomains="true">********</domain>
    </domain-config>
</network-security-config>
```

## Migration Steps

1. **Add Dependencies**: Add the OkHttp dependencies to your `build.gradle`
2. **Add Permissions**: Ensure network permissions are in your manifest
3. **Copy Classes**: Copy all the network and model classes to your project
4. **Update Imports**: Update your fragment imports to use the new DataItem model
5. **Replace Firebase Calls**: Replace Firebase database calls with API service calls
6. **Test**: Test each operation to ensure it works with the new backend

## API Configuration

Update the API base URL in `ApiClient.java` to match your server:

```java
private static final String BASE_URL = "http://your-server-ip/MtcInvoiceMasudvi/api/";
```

## Testing

After adding dependencies and classes:

1. Build the project to ensure no compilation errors
2. Test network connectivity to your API server
3. Test each CRUD operation individually
4. Verify file upload/download functionality
5. Test offline scenarios with local database

## Troubleshooting

### Common Issues:

1. **Network Security Exception**: Add network security config for HTTP
2. **Compilation Errors**: Ensure all imports are correct
3. **API Connection Failed**: Check server IP and port
4. **JSON Parsing Errors**: Verify model classes match API response format

### Debug Tips:

1. Enable HTTP logging to see request/response details
2. Use Android Studio's Network Inspector
3. Check Logcat for detailed error messages
4. Test API endpoints directly with Postman or browser
