<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:layout_gravity="center_vertical|center_horizontal"
    android:gravity="center_vertical|center_horizontal"
    android:background="@drawable/alert_dialog_background"
    android:padding="16dp">

    <TextView
        android:id="@+id/title_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:text="Enter file name"
        android:layout_marginBottom="16dp" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/file_name_input_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/alert_dialog_background"
        android:hint="Enter File Name"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        android:layout_marginHorizontal="16dp"
        android:layout_marginVertical="8dp">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/file_name_input"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text"
            android:maxLines="1"
            android:padding="16dp"
            android:fontFamily="@font/timesnewromanbold"
            android:textSize="16sp" />

    </com.google.android.material.textfield.TextInputLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="16dp">

        <Button
            android:id="@+id/cancel_button"
            android:layout_width="0dp"
            android:layout_margin="3dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Cancel"
            android:textColor="@color/white"
            android:background="@drawable/button_selector_effect"/>

        <Button
            android:id="@+id/save_button"
            android:layout_width="0dp"
            android:layout_margin="3dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Save"
            android:textColor="@color/white"
            android:background="@drawable/button_selector_effect" />

    </LinearLayout>

</LinearLayout>