package com.official.invoicegenarator;

import android.os.Bundle;
import androidx.fragment.app.Fragment;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Toast;

import com.official.invoicegenarator.network.ApiService;
import com.official.invoicegenarator.network.ApiCallback;
import com.official.invoicegenarator.models.DataItem;
import com.official.invoicegenarator.database.InvoiceDataHelper;

// Fragment class for uploading data
public class DataUploadFragment extends Fragment {

    private EditText editTextDescription, editTextLocation, editTextQR, editTextLPO, editTextINB, editTextAmount, editTextW_A, editTextPaymentStatus;
    private Button buttonUpload;
    private ApiService apiService;
    private InvoiceDataHelper dbHelper;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View view = inflater.inflate(R.layout.fragment_data_upload, container, false);

        // Initialize API service and database helper
        apiService = new ApiService();
        dbHelper = InvoiceDataHelper.getInstance(getContext());

        // Initialize UI components
        editTextDescription = view.findViewById(R.id.editTextDescription);
        editTextLocation = view.findViewById(R.id.editTextLocation);
        editTextQR = view.findViewById(R.id.editTextQR);
        editTextLPO = view.findViewById(R.id.editTextLPO);
        editTextINB = view.findViewById(R.id.editTextINB);
        editTextAmount = view.findViewById(R.id.editTextAmount);
        editTextW_A = view.findViewById(R.id.editTextW_A);
        editTextPaymentStatus = view.findViewById(R.id.payment_status);
        buttonUpload = view.findViewById(R.id.buttonUpload);

        buttonUpload.setOnClickListener(v -> uploadData());

        return view;
    }

    private void uploadData() {
        String description = editTextDescription.getText().toString().trim();
        String location = editTextLocation.getText().toString().trim();
        String qr = editTextQR.getText().toString().trim();
        String lpo = editTextLPO.getText().toString().trim();
        String inb = editTextINB.getText().toString().trim();
        String amount = editTextAmount.getText().toString().trim();
        String w_a = editTextW_A.getText().toString().trim();
        String paymentStatus = editTextPaymentStatus.getText().toString().trim();

        if (!description.isEmpty() && !location.isEmpty() && !qr.isEmpty() && !lpo.isEmpty() && !inb.isEmpty() && !amount.isEmpty() && !w_a.isEmpty() && !paymentStatus.isEmpty()) {

            long timestamp = System.currentTimeMillis(); // Get the current timestamp

            // Create DataItem using the new model
            DataItem dataItem = new DataItem(description, location, qr, lpo, inb, amount, w_a, paymentStatus, timestamp);

            // Generate Firebase-style ID
            dataItem.setId(generatePushKey());

            // Save locally first (for offline support)
            long localId = dbHelper.insertInvoiceItem(dataItem);

            if (localId != -1) {
                // Try to sync with server
                apiService.createInvoiceItem(dataItem, new ApiCallback<DataItem>() {
                    @Override
                    public void onSuccess(DataItem result) {
                        // Mark as synced in local database
                        result.setSynced(true);
                        dbHelper.updateInvoiceItem(result);

                        // Update UI on main thread
                        if (getActivity() != null) {
                            getActivity().runOnUiThread(() -> {
                                Toast.makeText(getContext(), "Data uploaded successfully", Toast.LENGTH_SHORT).show();
                                clearFields();
                            });
                        }
                    }

                    @Override
                    public void onError(String error) {
                        // Keep in local database for later sync
                        if (getActivity() != null) {
                            getActivity().runOnUiThread(() -> {
                                Toast.makeText(getContext(), "Saved locally, will sync when online", Toast.LENGTH_SHORT).show();
                                clearFields();
                            });
                        }
                    }
                });
            } else {
                Toast.makeText(getContext(), "Failed to save data", Toast.LENGTH_SHORT).show();
            }
        } else {
            Toast.makeText(getContext(), "Please fill all fields", Toast.LENGTH_SHORT).show();
        }
    }

    private void clearFields() {
        editTextDescription.setText("");
        editTextLocation.setText("");
        editTextQR.setText("");
        editTextLPO.setText("");
        editTextINB.setText("");
        editTextAmount.setText("");
        editTextW_A.setText("");
        editTextPaymentStatus.setText("");
    }

    /**
     * Generate Firebase-style push key
     */
    private String generatePushKey() {
        String chars = "-0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ_abcdefghijklmnopqrstuvwxyz";
        StringBuilder key = new StringBuilder();
        for (int i = 0; i < 20; i++) {
            key.append(chars.charAt((int) (Math.random() * chars.length())));
        }
        return key.toString();
    }
}