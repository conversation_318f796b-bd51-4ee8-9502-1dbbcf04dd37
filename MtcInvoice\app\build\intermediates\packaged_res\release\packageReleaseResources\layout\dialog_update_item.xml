<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Description">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/editTextDescription"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/timesnewroman"
                android:textStyle="bold"
                android:inputType="text" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Location">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/editTextLocation"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/timesnewroman"
                android:textStyle="bold"
                android:inputType="text" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="QR Code">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/editTextQr"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/timesnewroman"
                android:textStyle="bold"
                android:inputType="text" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="LPO">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/editTextLpo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/timesnewroman"
                android:textStyle="bold"
                android:inputType="text" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Invoice Number">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/editTextInb"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textStyle="bold"
                android:fontFamily="@font/timesnewroman"
                android:inputType="text" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Amount">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/editTextAmount"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/timesnewroman"
                android:textStyle="bold"
                android:inputType="text" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="W/A">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/editTextW_a"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/timesnewroman"
                android:textStyle="bold"
                android:inputType="text" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Payment Status">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/payment_status_update"
                android:layout_width="match_parent"
                android:fontFamily="@font/timesnewroman"
                android:layout_height="wrap_content"
                android:textStyle="bold"
                android:inputType="text" />
        </com.google.android.material.textfield.TextInputLayout>

    </LinearLayout>

</androidx.core.widget.NestedScrollView>