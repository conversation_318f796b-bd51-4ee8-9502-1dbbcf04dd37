package com.official.invoicegenarator.database;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import android.util.Log;

import com.official.invoicegenarator.models.DataItem;
import com.official.invoicegenarator.models.FileInfo;

import java.util.ArrayList;
import java.util.List;

/**
 * SQLite database helper for local invoice data storage
 * Extends existing expense tracking with invoice data and file management
 */
public class InvoiceDataHelper extends SQLiteOpenHelper {
    private static final String TAG = "InvoiceDataHelper";
    
    // Database configuration
    private static final String DATABASE_NAME = "MtcInvoice.db";
    private static final int DATABASE_VERSION = 1;
    
    // Table names
    private static final String TABLE_INVOICE_DATA = "invoice_data_items";
    private static final String TABLE_FILES = "files";
    private static final String TABLE_SYNC_STATUS = "sync_status";
    
    // Invoice data table columns
    private static final String COLUMN_ID = "id";
    private static final String COLUMN_LOCAL_ID = "local_id";
    private static final String COLUMN_DESCRIPTION = "description";
    private static final String COLUMN_LOCATION = "location";
    private static final String COLUMN_QR = "qr";
    private static final String COLUMN_LPO = "lpo";
    private static final String COLUMN_INB = "inb";
    private static final String COLUMN_AMOUNT = "amount";
    private static final String COLUMN_W_A = "w_a";
    private static final String COLUMN_PAYMENT_STATUS = "payment_status";
    private static final String COLUMN_TIMESTAMP = "timestamp";
    private static final String COLUMN_CURRENT_DATE_TIME = "current_date_time";
    private static final String COLUMN_IS_SYNCED = "is_synced";
    private static final String COLUMN_LAST_SYNC_TIME = "last_sync_time";
    private static final String COLUMN_SYNC_STATUS = "sync_status";
    private static final String COLUMN_IS_DELETED = "is_deleted";
    private static final String COLUMN_CREATED_AT = "created_at";
    private static final String COLUMN_UPDATED_AT = "updated_at";
    
    // Files table columns
    private static final String FILE_ID = "id";
    private static final String FILE_ORIGINAL_NAME = "original_name";
    private static final String FILE_STORED_NAME = "stored_name";
    private static final String FILE_LOCAL_PATH = "local_path";
    private static final String FILE_REMOTE_PATH = "remote_path";
    private static final String FILE_SIZE = "file_size";
    private static final String FILE_TYPE = "file_type";
    private static final String FILE_EXTENSION = "file_extension";
    private static final String FILE_HASH = "file_hash";
    private static final String FILE_IS_DOWNLOADED = "is_downloaded";
    private static final String FILE_IS_UPLOADED = "is_uploaded";
    private static final String FILE_DOWNLOAD_TIME = "download_time";
    private static final String FILE_UPLOAD_TIME = "upload_time";
    private static final String FILE_SYNC_STATUS = "sync_status";
    
    // Create table statements
    private static final String CREATE_INVOICE_DATA_TABLE = 
        "CREATE TABLE " + TABLE_INVOICE_DATA + " (" +
        COLUMN_LOCAL_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
        COLUMN_ID + " TEXT UNIQUE, " +
        COLUMN_DESCRIPTION + " TEXT NOT NULL, " +
        COLUMN_LOCATION + " TEXT NOT NULL, " +
        COLUMN_QR + " TEXT NOT NULL, " +
        COLUMN_LPO + " TEXT NOT NULL, " +
        COLUMN_INB + " TEXT NOT NULL, " +
        COLUMN_AMOUNT + " TEXT NOT NULL, " +
        COLUMN_W_A + " TEXT NOT NULL, " +
        COLUMN_PAYMENT_STATUS + " TEXT NOT NULL, " +
        COLUMN_TIMESTAMP + " INTEGER NOT NULL, " +
        COLUMN_CURRENT_DATE_TIME + " TEXT, " +
        COLUMN_IS_SYNCED + " INTEGER DEFAULT 0, " +
        COLUMN_LAST_SYNC_TIME + " INTEGER DEFAULT 0, " +
        COLUMN_SYNC_STATUS + " TEXT DEFAULT 'pending', " +
        COLUMN_IS_DELETED + " INTEGER DEFAULT 0, " +
        COLUMN_CREATED_AT + " INTEGER NOT NULL, " +
        COLUMN_UPDATED_AT + " INTEGER NOT NULL" +
        ")";
    
    private static final String CREATE_FILES_TABLE = 
        "CREATE TABLE " + TABLE_FILES + " (" +
        FILE_ID + " TEXT PRIMARY KEY, " +
        FILE_ORIGINAL_NAME + " TEXT NOT NULL, " +
        FILE_STORED_NAME + " TEXT, " +
        FILE_LOCAL_PATH + " TEXT, " +
        FILE_REMOTE_PATH + " TEXT, " +
        FILE_SIZE + " INTEGER NOT NULL, " +
        FILE_TYPE + " TEXT, " +
        FILE_EXTENSION + " TEXT, " +
        FILE_HASH + " TEXT, " +
        FILE_IS_DOWNLOADED + " INTEGER DEFAULT 0, " +
        FILE_IS_UPLOADED + " INTEGER DEFAULT 0, " +
        FILE_DOWNLOAD_TIME + " INTEGER DEFAULT 0, " +
        FILE_UPLOAD_TIME + " INTEGER DEFAULT 0, " +
        FILE_SYNC_STATUS + " TEXT DEFAULT 'pending'" +
        ")";
    
    private static final String CREATE_SYNC_STATUS_TABLE = 
        "CREATE TABLE " + TABLE_SYNC_STATUS + " (" +
        "id INTEGER PRIMARY KEY AUTOINCREMENT, " +
        "table_name TEXT NOT NULL, " +
        "last_sync_time INTEGER DEFAULT 0, " +
        "sync_status TEXT DEFAULT 'pending'" +
        ")";
    
    private static InvoiceDataHelper instance;
    
    private InvoiceDataHelper(Context context) {
        super(context, DATABASE_NAME, null, DATABASE_VERSION);
    }
    
    public static synchronized InvoiceDataHelper getInstance(Context context) {
        if (instance == null) {
            instance = new InvoiceDataHelper(context.getApplicationContext());
        }
        return instance;
    }
    
    @Override
    public void onCreate(SQLiteDatabase db) {
        try {
            db.execSQL(CREATE_INVOICE_DATA_TABLE);
            db.execSQL(CREATE_FILES_TABLE);
            db.execSQL(CREATE_SYNC_STATUS_TABLE);
            
            // Create indexes for better performance
            db.execSQL("CREATE INDEX idx_invoice_timestamp ON " + TABLE_INVOICE_DATA + "(" + COLUMN_TIMESTAMP + ")");
            db.execSQL("CREATE INDEX idx_invoice_sync_status ON " + TABLE_INVOICE_DATA + "(" + COLUMN_SYNC_STATUS + ")");
            db.execSQL("CREATE INDEX idx_files_local_path ON " + TABLE_FILES + "(" + FILE_LOCAL_PATH + ")");
            
            Log.d(TAG, "Database tables created successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error creating database tables", e);
        }
    }
    
    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        Log.w(TAG, "Upgrading database from version " + oldVersion + " to " + newVersion);
        
        // For now, drop and recreate tables
        // In production, implement proper migration
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_INVOICE_DATA);
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_FILES);
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_SYNC_STATUS);
        onCreate(db);
    }
    
    // ==================== Invoice Data CRUD Operations ====================
    
    /**
     * Insert new invoice data item
     */
    public long insertInvoiceItem(DataItem item) {
        SQLiteDatabase db = this.getWritableDatabase();
        ContentValues values = new ContentValues();
        
        values.put(COLUMN_ID, item.getId());
        values.put(COLUMN_DESCRIPTION, item.getDescription());
        values.put(COLUMN_LOCATION, item.getLocation());
        values.put(COLUMN_QR, item.getQr());
        values.put(COLUMN_LPO, item.getLpo());
        values.put(COLUMN_INB, item.getInb());
        values.put(COLUMN_AMOUNT, item.getAmount());
        values.put(COLUMN_W_A, item.getW_a());
        values.put(COLUMN_PAYMENT_STATUS, item.getPaymentStatus());
        values.put(COLUMN_TIMESTAMP, item.getTimestamp());
        values.put(COLUMN_CURRENT_DATE_TIME, item.getCurrentDateTime());
        values.put(COLUMN_IS_SYNCED, item.isSynced() ? 1 : 0);
        values.put(COLUMN_LAST_SYNC_TIME, item.getLastSyncTime());
        values.put(COLUMN_SYNC_STATUS, item.getSyncStatus());
        values.put(COLUMN_IS_DELETED, item.isDeleted() ? 1 : 0);
        values.put(COLUMN_CREATED_AT, item.getCreatedAt());
        values.put(COLUMN_UPDATED_AT, item.getUpdatedAt());
        
        try {
            long localId = db.insert(TABLE_INVOICE_DATA, null, values);
            if (localId != -1) {
                item.setLocalId(String.valueOf(localId));
            }
            return localId;
        } catch (Exception e) {
            Log.e(TAG, "Error inserting invoice item", e);
            return -1;
        } finally {
            db.close();
        }
    }
    
    /**
     * Get all invoice data items
     */
    public List<DataItem> getAllInvoiceItems() {
        return getInvoiceItems(COLUMN_IS_DELETED + " = 0", null, COLUMN_TIMESTAMP + " DESC");
    }
    
    /**
     * Get invoice items with custom conditions
     */
    public List<DataItem> getInvoiceItems(String selection, String[] selectionArgs, String orderBy) {
        List<DataItem> items = new ArrayList<>();
        SQLiteDatabase db = this.getReadableDatabase();
        
        try {
            Cursor cursor = db.query(TABLE_INVOICE_DATA, null, selection, selectionArgs, null, null, orderBy);
            
            if (cursor.moveToFirst()) {
                do {
                    DataItem item = cursorToDataItem(cursor);
                    items.add(item);
                } while (cursor.moveToNext());
            }
            cursor.close();
        } catch (Exception e) {
            Log.e(TAG, "Error getting invoice items", e);
        } finally {
            db.close();
        }
        
        return items;
    }
    
    /**
     * Get invoice item by ID
     */
    public DataItem getInvoiceItemById(String id) {
        SQLiteDatabase db = this.getReadableDatabase();
        DataItem item = null;
        
        try {
            Cursor cursor = db.query(TABLE_INVOICE_DATA, null, 
                COLUMN_ID + " = ?", new String[]{id}, null, null, null);
            
            if (cursor.moveToFirst()) {
                item = cursorToDataItem(cursor);
            }
            cursor.close();
        } catch (Exception e) {
            Log.e(TAG, "Error getting invoice item by ID", e);
        } finally {
            db.close();
        }
        
        return item;
    }
    
    /**
     * Update invoice data item
     */
    public int updateInvoiceItem(DataItem item) {
        SQLiteDatabase db = this.getWritableDatabase();
        ContentValues values = new ContentValues();
        
        values.put(COLUMN_DESCRIPTION, item.getDescription());
        values.put(COLUMN_LOCATION, item.getLocation());
        values.put(COLUMN_QR, item.getQr());
        values.put(COLUMN_LPO, item.getLpo());
        values.put(COLUMN_INB, item.getInb());
        values.put(COLUMN_AMOUNT, item.getAmount());
        values.put(COLUMN_W_A, item.getW_a());
        values.put(COLUMN_PAYMENT_STATUS, item.getPaymentStatus());
        values.put(COLUMN_TIMESTAMP, item.getTimestamp());
        values.put(COLUMN_CURRENT_DATE_TIME, item.getCurrentDateTime());
        values.put(COLUMN_IS_SYNCED, item.isSynced() ? 1 : 0);
        values.put(COLUMN_LAST_SYNC_TIME, item.getLastSyncTime());
        values.put(COLUMN_SYNC_STATUS, item.getSyncStatus());
        values.put(COLUMN_IS_DELETED, item.isDeleted() ? 1 : 0);
        values.put(COLUMN_UPDATED_AT, System.currentTimeMillis());
        
        try {
            return db.update(TABLE_INVOICE_DATA, values, COLUMN_ID + " = ?", new String[]{item.getId()});
        } catch (Exception e) {
            Log.e(TAG, "Error updating invoice item", e);
            return 0;
        } finally {
            db.close();
        }
    }
    
    /**
     * Delete invoice data item (soft delete)
     */
    public int deleteInvoiceItem(String id) {
        SQLiteDatabase db = this.getWritableDatabase();
        ContentValues values = new ContentValues();
        
        values.put(COLUMN_IS_DELETED, 1);
        values.put(COLUMN_IS_SYNCED, 0);
        values.put(COLUMN_SYNC_STATUS, "pending");
        values.put(COLUMN_UPDATED_AT, System.currentTimeMillis());
        
        try {
            return db.update(TABLE_INVOICE_DATA, values, COLUMN_ID + " = ?", new String[]{id});
        } catch (Exception e) {
            Log.e(TAG, "Error deleting invoice item", e);
            return 0;
        } finally {
            db.close();
        }
    }
    
    /**
     * Get items that need sync
     */
    public List<DataItem> getUnsyncedItems() {
        return getInvoiceItems(COLUMN_IS_SYNCED + " = 0 OR " + COLUMN_SYNC_STATUS + " = 'pending' OR " + 
                             COLUMN_SYNC_STATUS + " = 'error'", null, COLUMN_UPDATED_AT + " ASC");
    }
    
    /**
     * Convert cursor to DataItem
     */
    private DataItem cursorToDataItem(Cursor cursor) {
        DataItem item = new DataItem();
        
        item.setLocalId(cursor.getString(cursor.getColumnIndex(COLUMN_LOCAL_ID)));
        item.setId(cursor.getString(cursor.getColumnIndex(COLUMN_ID)));
        item.setDescription(cursor.getString(cursor.getColumnIndex(COLUMN_DESCRIPTION)));
        item.setLocation(cursor.getString(cursor.getColumnIndex(COLUMN_LOCATION)));
        item.setQr(cursor.getString(cursor.getColumnIndex(COLUMN_QR)));
        item.setLpo(cursor.getString(cursor.getColumnIndex(COLUMN_LPO)));
        item.setInb(cursor.getString(cursor.getColumnIndex(COLUMN_INB)));
        item.setAmount(cursor.getString(cursor.getColumnIndex(COLUMN_AMOUNT)));
        item.setW_a(cursor.getString(cursor.getColumnIndex(COLUMN_W_A)));
        item.setPaymentStatus(cursor.getString(cursor.getColumnIndex(COLUMN_PAYMENT_STATUS)));
        item.setTimestamp(cursor.getLong(cursor.getColumnIndex(COLUMN_TIMESTAMP)));
        item.setCurrentDateTime(cursor.getString(cursor.getColumnIndex(COLUMN_CURRENT_DATE_TIME)));
        item.setSynced(cursor.getInt(cursor.getColumnIndex(COLUMN_IS_SYNCED)) == 1);
        item.setLastSyncTime(cursor.getLong(cursor.getColumnIndex(COLUMN_LAST_SYNC_TIME)));
        item.setSyncStatus(cursor.getString(cursor.getColumnIndex(COLUMN_SYNC_STATUS)));
        item.setDeleted(cursor.getInt(cursor.getColumnIndex(COLUMN_IS_DELETED)) == 1);
        item.setCreatedAt(cursor.getLong(cursor.getColumnIndex(COLUMN_CREATED_AT)));
        item.setUpdatedAt(cursor.getLong(cursor.getColumnIndex(COLUMN_UPDATED_AT)));
        
        return item;
    }
    
    // ==================== File Management Operations ====================
    
    /**
     * Insert file info
     */
    public long insertFileInfo(FileInfo fileInfo) {
        SQLiteDatabase db = this.getWritableDatabase();
        ContentValues values = new ContentValues();
        
        values.put(FILE_ID, fileInfo.getId());
        values.put(FILE_ORIGINAL_NAME, fileInfo.getOriginalName());
        values.put(FILE_STORED_NAME, fileInfo.getStoredName());
        values.put(FILE_LOCAL_PATH, fileInfo.getLocalPath());
        values.put(FILE_REMOTE_PATH, fileInfo.getRelativePath());
        values.put(FILE_SIZE, fileInfo.getFileSize());
        values.put(FILE_TYPE, fileInfo.getFileType());
        values.put(FILE_EXTENSION, fileInfo.getFileExtension());
        values.put(FILE_HASH, fileInfo.getFileHash());
        values.put(FILE_IS_DOWNLOADED, fileInfo.isDownloaded() ? 1 : 0);
        values.put(FILE_IS_UPLOADED, fileInfo.isUploaded() ? 1 : 0);
        values.put(FILE_DOWNLOAD_TIME, fileInfo.getDownloadTime());
        values.put(FILE_SYNC_STATUS, fileInfo.getSyncStatus());
        
        try {
            return db.insert(TABLE_FILES, null, values);
        } catch (Exception e) {
            Log.e(TAG, "Error inserting file info", e);
            return -1;
        } finally {
            db.close();
        }
    }
    
    /**
     * Get all files
     */
    public List<FileInfo> getAllFiles() {
        List<FileInfo> files = new ArrayList<>();
        SQLiteDatabase db = this.getReadableDatabase();
        
        try {
            Cursor cursor = db.query(TABLE_FILES, null, null, null, null, null, FILE_ORIGINAL_NAME + " ASC");
            
            if (cursor.moveToFirst()) {
                do {
                    FileInfo fileInfo = cursorToFileInfo(cursor);
                    files.add(fileInfo);
                } while (cursor.moveToNext());
            }
            cursor.close();
        } catch (Exception e) {
            Log.e(TAG, "Error getting files", e);
        } finally {
            db.close();
        }
        
        return files;
    }
    
    /**
     * Convert cursor to FileInfo
     */
    private FileInfo cursorToFileInfo(Cursor cursor) {
        FileInfo fileInfo = new FileInfo();
        
        fileInfo.setId(cursor.getString(cursor.getColumnIndex(FILE_ID)));
        fileInfo.setOriginalName(cursor.getString(cursor.getColumnIndex(FILE_ORIGINAL_NAME)));
        fileInfo.setStoredName(cursor.getString(cursor.getColumnIndex(FILE_STORED_NAME)));
        fileInfo.setLocalPath(cursor.getString(cursor.getColumnIndex(FILE_LOCAL_PATH)));
        fileInfo.setRelativePath(cursor.getString(cursor.getColumnIndex(FILE_REMOTE_PATH)));
        fileInfo.setFileSize(cursor.getLong(cursor.getColumnIndex(FILE_SIZE)));
        fileInfo.setFileType(cursor.getString(cursor.getColumnIndex(FILE_TYPE)));
        fileInfo.setFileExtension(cursor.getString(cursor.getColumnIndex(FILE_EXTENSION)));
        fileInfo.setFileHash(cursor.getString(cursor.getColumnIndex(FILE_HASH)));
        fileInfo.setDownloaded(cursor.getInt(cursor.getColumnIndex(FILE_IS_DOWNLOADED)) == 1);
        fileInfo.setUploaded(cursor.getInt(cursor.getColumnIndex(FILE_IS_UPLOADED)) == 1);
        fileInfo.setDownloadTime(cursor.getLong(cursor.getColumnIndex(FILE_DOWNLOAD_TIME)));
        fileInfo.setSyncStatus(cursor.getString(cursor.getColumnIndex(FILE_SYNC_STATUS)));
        
        return fileInfo;
    }
    
    // ==================== Utility Methods ====================
    
    /**
     * Clear all data (for testing)
     */
    public void clearAllData() {
        SQLiteDatabase db = this.getWritableDatabase();
        try {
            db.delete(TABLE_INVOICE_DATA, null, null);
            db.delete(TABLE_FILES, null, null);
            db.delete(TABLE_SYNC_STATUS, null, null);
        } catch (Exception e) {
            Log.e(TAG, "Error clearing data", e);
        } finally {
            db.close();
        }
    }
    
    /**
     * Get database statistics
     */
    public String getDatabaseStats() {
        SQLiteDatabase db = this.getReadableDatabase();
        StringBuilder stats = new StringBuilder();
        
        try {
            Cursor cursor = db.rawQuery("SELECT COUNT(*) FROM " + TABLE_INVOICE_DATA, null);
            if (cursor.moveToFirst()) {
                stats.append("Invoice Items: ").append(cursor.getInt(0)).append("\n");
            }
            cursor.close();
            
            cursor = db.rawQuery("SELECT COUNT(*) FROM " + TABLE_FILES, null);
            if (cursor.moveToFirst()) {
                stats.append("Files: ").append(cursor.getInt(0)).append("\n");
            }
            cursor.close();
            
            cursor = db.rawQuery("SELECT COUNT(*) FROM " + TABLE_INVOICE_DATA + " WHERE " + COLUMN_IS_SYNCED + " = 0", null);
            if (cursor.moveToFirst()) {
                stats.append("Unsynced Items: ").append(cursor.getInt(0));
            }
            cursor.close();
            
        } catch (Exception e) {
            Log.e(TAG, "Error getting database stats", e);
            stats.append("Error getting stats");
        } finally {
            db.close();
        }
        
        return stats.toString();
    }
}
