<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.1" type="partial_results">
    <map id="NotificationPermission">
        <location id="class"
            file="$GRADLE_USER_HOME/caches/8.14.2/transforms/e97a647b3c286c6eaf4635d2fa2f7767/transformed/jetified-glide-4.12.0/jars/classes.jar"/>
        <entry
            name="className"
            string="com/bumptech/glide/request/target/NotificationTarget"/>
    </map>
    <map id="UnsafeImplicitIntentLaunch">
            <map id="actionsSent">
                    <map id="android.intent.action.VIEW (used to start an activity)">
                        <location id="0"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/DownloadListActivity.java"
                            line="411"
                            column="33"
                            startOffset="17758"
                            endLine="411"
                            endColumn="63"
                            endOffset="17788"/>
                        <location id="1"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/PdfViewerActivity.java"
                            line="26"
                            column="25"
                            startOffset="795"
                            endLine="26"
                            endColumn="55"
                            endOffset="825"/>
                    </map>
            </map>
    </map>
    <map id="UnsafeIntentLaunch">
            <map id="unprotected">
                <entry
                    name="com.official.invoicegenarator.MainActivity"
                    boolean="true"/>
            </map>
    </map>
    <map id="UnusedResources">
        <location id="R.anim.rotate_in"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/rotate_in.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="7"
            endColumn="30"
            endOffset="247"/>
        <location id="R.anim.rotate_out"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/anim/rotate_out.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="7"
            endColumn="30"
            endOffset="247"/>
        <location id="R.color.colorPrimary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="13"
            column="12"
            startOffset="504"
            endLine="13"
            endColumn="31"
            endOffset="523"/>
        <location id="R.color.colorPrimaryDark"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="16"
            column="12"
            startOffset="628"
            endLine="16"
            endColumn="35"
            endOffset="651"/>
        <location id="R.color.orange"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="11"
            column="12"
            startOffset="417"
            endLine="11"
            endColumn="25"
            endOffset="430"/>
        <location id="R.drawable.accounts_bg"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/accounts_bg.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="5"
            endColumn="9"
            endOffset="189"/>
        <location id="R.drawable.backgroundwttop"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/backgroundwttop.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="14"
            endColumn="14"
            endOffset="439"/>
        <location id="R.drawable.category_bg"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/category_bg.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="4"
            endColumn="9"
            endOffset="168"/>
        <location id="R.drawable.custom_horizontal_thumb"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/custom_horizontal_thumb.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="5"
            endColumn="9"
            endOffset="187"/>
        <location id="R.drawable.default_selector"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/default_selector.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="6"
            endColumn="9"
            endOffset="238"/>
        <location id="R.drawable.dialog_bg"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/dialog_bg.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="5"
            endColumn="9"
            endOffset="189"/>
        <location id="R.drawable.divider"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/divider.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="7"
            endColumn="9"
            endOffset="243"/>
        <location id="R.drawable.empty_state"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/empty_state.png"/>
        <location id="R.drawable.expense_selector"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/expense_selector.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="6"
            endColumn="9"
            endOffset="243"/>
        <location id="R.drawable.ic_accounts"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_accounts.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="4"
            endColumn="10"
            endOffset="1111"/>
        <location id="R.drawable.ic_book"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_book.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="6"
            endColumn="10"
            endOffset="1546"/>
        <location id="R.drawable.ic_business"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_business.png"/>
        <location id="R.drawable.ic_chart"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_chart.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="4"
            endColumn="10"
            endOffset="306"/>
        <location id="R.drawable.ic_chevron"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_chevron.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="7"
            endColumn="10"
            endOffset="393"/>
        <location id="R.drawable.ic_investment"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_investment.png"/>
        <location id="R.drawable.ic_launcher_foreground"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable-v24/ic_launcher_foreground.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="30"
            endColumn="10"
            endOffset="1702"/>
        <location id="R.drawable.ic_loan"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_loan.png"/>
        <location id="R.drawable.ic_more"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_more.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="4"
            endColumn="10"
            endOffset="347"/>
        <location id="R.drawable.ic_other"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_other.png"/>
        <location id="R.drawable.ic_plus"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_plus.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="4"
            endColumn="10"
            endOffset="488"/>
        <location id="R.drawable.ic_rent"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_rent.png"/>
        <location id="R.drawable.ic_salary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_salary.png"/>
        <location id="R.drawable.ic_star"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_star.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="4"
            endColumn="10"
            endOffset="509"/>
        <location id="R.drawable.img"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/img.png"/>
        <location id="R.drawable.income_selector"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/income_selector.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="6"
            endColumn="9"
            endOffset="243"/>
        <location id="R.drawable.invoice"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/invoice.png"/>
        <location id="R.drawable.invoicelogo"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/invoicelogo.png"/>
        <location id="R.drawable.ltbback"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ltbback.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="16"
            endColumn="14"
            endOffset="540"/>
        <location id="R.drawable.rightbgadd"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/rightbgadd.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="14"
            endColumn="14"
            endOffset="460"/>
        <location id="R.drawable.rtbback"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/rtbback.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="14"
            endColumn="14"
            endOffset="477"/>
        <location id="R.drawable.underline"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/underline.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="5"
            endColumn="9"
            endOffset="218"/>
        <location id="R.layout.dialog_add_income"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_add_income.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="52"
            endColumn="53"
            endOffset="2050"/>
        <location id="R.layout.dialog_progress"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_progress.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="23"
            endColumn="16"
            endOffset="834"/>
        <location id="R.layout.ivtraker_item_row"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/ivtraker_item_row.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="96"
            endColumn="16"
            endOffset="3085"/>
        <location id="R.layout.vertical_border_black"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/vertical_border_black.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="19"
            endColumn="53"
            endOffset="732"/>
        <location id="R.raw.click_sound"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/raw/click_sound.mp3"/>
        <location id="R.string.add_vat"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="36"
            column="13"
            startOffset="2034"
            endLine="36"
            endColumn="27"
            endOffset="2048"/>
        <location id="R.string.address"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="5"
            column="13"
            startOffset="194"
            endLine="5"
            endColumn="27"
            endOffset="208"/>
        <location id="R.string.amount"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="24"
            column="13"
            startOffset="1149"
            endLine="24"
            endColumn="26"
            endOffset="1162"/>
        <location id="R.string.barka"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="26"
            column="13"
            startOffset="1227"
            endLine="26"
            endColumn="25"
            endOffset="1239"/>
        <location id="R.string.burimi"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="33"
            column="13"
            startOffset="1827"
            endLine="33"
            endColumn="26"
            endOffset="1840"/>
        <location id="R.string.company_name"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="3"
            column="13"
            startOffset="82"
            endLine="3"
            endColumn="32"
            endOffset="101"/>
        <location id="R.string.cr_no"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="7"
            column="13"
            startOffset="307"
            endLine="7"
            endColumn="25"
            endOffset="319"/>
        <location id="R.string.date"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="17"
            column="13"
            startOffset="861"
            endLine="17"
            endColumn="24"
            endOffset="872"/>
        <location id="R.string.default_off_text"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="40"
            column="13"
            startOffset="2382"
            endLine="40"
            endColumn="36"
            endOffset="2405"/>
        <location id="R.string.default_on_text"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="41"
            column="13"
            startOffset="2431"
            endLine="41"
            endColumn="35"
            endOffset="2453"/>
        <location id="R.string.description"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="16"
            column="13"
            startOffset="809"
            endLine="16"
            endColumn="31"
            endOffset="827"/>
        <location id="R.string.email"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="8"
            column="13"
            startOffset="358"
            endLine="8"
            endColumn="25"
            endOffset="370"/>
        <location id="R.string.hello_blank_fragment"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="43"
            column="13"
            startOffset="2536"
            endLine="43"
            endColumn="40"
            endOffset="2563"/>
        <location id="R.string.location"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="19"
            column="13"
            startOffset="939"
            endLine="19"
            endColumn="28"
            endOffset="954"/>
        <location id="R.string.mhada"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="34"
            column="13"
            startOffset="1897"
            endLine="34"
            endColumn="25"
            endOffset="1909"/>
        <location id="R.string.mussina"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="27"
            column="13"
            startOffset="1314"
            endLine="27"
            endColumn="27"
            endOffset="1328"/>
        <location id="R.string.omr"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="25"
            column="13"
            startOffset="1191"
            endLine="25"
            endColumn="23"
            endOffset="1201"/>
        <location id="R.string.phone"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="6"
            column="13"
            startOffset="259"
            endLine="6"
            endColumn="25"
            endOffset="271"/>
        <location id="R.string.po_box"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="4"
            column="13"
            startOffset="142"
            endLine="4"
            endColumn="26"
            endOffset="155"/>
        <location id="R.string.qr_no"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="18"
            column="13"
            startOffset="899"
            endLine="18"
            endColumn="25"
            endOffset="911"/>
        <location id="R.string.qty"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="21"
            column="13"
            startOffset="1027"
            endLine="21"
            endColumn="23"
            endOffset="1037"/>
        <location id="R.string.quotation"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="14"
            column="13"
            startOffset="679"
            endLine="14"
            endColumn="29"
            endOffset="695"/>
        <location id="R.string.saham"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="29"
            column="13"
            startOffset="1465"
            endLine="29"
            endColumn="25"
            endOffset="1477"/>
        <location id="R.string.sininah"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="32"
            column="13"
            startOffset="1751"
            endLine="32"
            endColumn="27"
            endOffset="1765"/>
        <location id="R.string.sohar_industrial"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="30"
            column="13"
            startOffset="1551"
            endLine="30"
            endColumn="36"
            endOffset="1574"/>
        <location id="R.string.subject"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="15"
            column="13"
            startOffset="727"
            endLine="15"
            endColumn="27"
            endOffset="741"/>
        <location id="R.string.suwaiq"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="28"
            column="13"
            startOffset="1394"
            endLine="28"
            endColumn="26"
            endOffset="1407"/>
        <location id="R.string.total_amount"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="38"
            column="13"
            startOffset="2166"
            endLine="38"
            endColumn="32"
            endOffset="2185"/>
        <location id="R.string.total_amount_excluding_vat"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="35"
            column="13"
            startOffset="1949"
            endLine="35"
            endColumn="46"
            endOffset="1982"/>
        <location id="R.string.total_amount_including_vat"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="37"
            column="13"
            startOffset="2081"
            endLine="37"
            endColumn="46"
            endOffset="2114"/>
        <location id="R.string.unit_price"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="23"
            column="13"
            startOffset="1099"
            endLine="23"
            endColumn="30"
            endOffset="1116"/>
        <location id="R.string.uom"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="22"
            column="13"
            startOffset="1063"
            endLine="22"
            endColumn="23"
            endOffset="1073"/>
        <location id="R.string.vat_in"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="20"
            column="13"
            startOffset="985"
            endLine="20"
            endColumn="26"
            endOffset="998"/>
        <location id="R.string.wadi_hibi"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="31"
            column="13"
            startOffset="1658"
            endLine="31"
            endColumn="29"
            endOffset="1674"/>
        <entry
            name="model"
            string="anim[fade_in(U),fade_out(U),rotate_in(D),rotate_out(D)],attr[actionBarSize(R),colorPrimaryVariant(R)],color[orange(D),white(U),black(U),green(U),colorAccent(U),gray(U),red(U),purple_200(U),purple_500(U),purple_700(U),teal_200(U),teal_700(U),colorPrimary(D),colorPrimaryDark(D)],drawable[accounts_bg(D),alert_dialog_background(U),background_curve_right(U),backgroundwttop(D),baseline_add_circle_outline_24(U),baseline_co_present_24(U),baseline_document_scanner_24(U),baseline_menu_24(U),baseline_remove_circle_outline_24(U),baseline_settings_24(U),baseline_settings_backup_restore_24(U),baseline_wallet_24(U),border_black(U),border_textview(U),btn_bankbackg(U),btn_bankr(U),btn_color_after(U),btn_color_before(U),button_selector_effect(U),card_view_elevation(U),category_bg(D),cell_background(U),converter_document(U),custom_horizontal_thumb(D),custom_vertical_thumb(U),default_selector(D),delete(U),dialog_bg(D),divider(D),download(U),downloadlist(U),empty_state(D),expense_selector(D),fingerback(U),fingerprint(U),fingerprintsetting(U),fingerprintthree(U),fingerprinttwo(U),first_invoice(U),ic_accounts(D),ic_book(D),ic_business(D),ic_chart(D),ic_chevron(D),ic_investment(D),ic_launcher_background(U),ic_loan(D),ic_more(D),ic_other(D),ic_plus(D),ic_rent(D),ic_salary(D),ic_search(U),ic_star(D),img(D),income_selector(D),invoice(D),invoice_traker(U),invoicelogo(D),iv_traker_upload(U),ltbback(D),moneybag(U),mtcsplash(U),navigationicon(U),rightbgadd(D),rounded(U),rounded_corner(U),rounded_corners(U),rounded_search_background(U),rtbback(D),second_invoice(U),signature(U),tround(U),underline(D),update_delete_iv_traker(U),upload(U),view(U),workerattendance(U),ic_launcher_foreground(D),ic_launcher_foreground_1(E)],font[timesnewromanbold(R),timesnewroman(R)],id[sortOptions(U),sortNewest(U),sortOldest(D),searchEditText(U),loadingProgressBar(U),recyclerView(U),sc_fingerprint(U),lay(D),toolbar(U),sView(D),bottomLayout(U),sllayout(U),include(D),linearLayout(D),idididi(D),banklay(U),signaturelay(U),signature_include(D),zoomControls(U),resetZoomBtn(U),downloads(U),downloadBtn(U),uploadBtn(U),add_column(U),remove_column(U),add_vat_column(U),remove_vat_column(U),add_back_layout(U),remove_back_layout(U),add_signature(U),tabLayout(U),viewPager(U),fragment_container(D),drawer_layout(U),linearLayout2(D),moneybagImageView(U),iv_worker_presentation(U),iv_invoice(U),iv_invoice_two(U),invoice_traker(U),nav_view(U),imageView(U),text_view(U),main(D),textView(D),bank_name_label(D),bank_name_value(D),account_no_label(D),account_no_value(D),account_name_holder_label(D),account_name_holder_value(D),linearLayout3(U),linearLayout4(U),date(U),qrTextView(U),subject(U),table_container(U),qrTextViewtwo(D),title_text(U),file_name_input_layout(D),file_name_input(U),cancel_button(U),save_button(U),etAmount(U),etCategory(U),etDate(U),etNote(U),btnPickDate(U),spCategory(U),tvDate(U),btnSave(D),exit_title(U),checkbox_confirm(U),button_cancel(U),button_yes(U),editTextNumber(U),textViewWords(U),buttonCopy(U),progressBar(D),progressMessage(D),fileNameInput(U),editTextDescription(U),editTextLocation(U),editTextQr(U),editTextLpo(U),editTextInb(U),editTextAmount(U),editTextW_a(U),payment_status_update(U),drag_handle(D),editTextQR(U),editTextLPO(U),editTextINB(U),editTextW_A(U),payment_status(U),buttonUpload(U),edit_text(U),pieChartExpenses(U),recyclerViewExpenses(U),btnAddExpense(U),recyclerViewIncome(U),lottieAnimationView(U),search_bar(U),serial(U),desc(D),text_time_date(U),text_description(U),text_location(U),text_qr(U),text_lpo(U),text_inb(U),text_amount(U),text_w_a(U),text_payment_status(U),button_edit(U),button_delete(U),tvCategory(U),tvAmount(U),tvNote(U),descriptionTextView(D),locationTextView(D),lpoTextView(D),inbTextView(D),amountTextView(D),waTextView(D),updateButton(D),deleteButton(U),progress_bar(U),loading_text(D),pdfName(U),pdfTimestamp(U),pdfSize(U),viewButton(U),downloadButton(U),signature_image(U),vat_layout(U),wallet(U),worker_attendence(U),nav_settings(U),action_open_dialog(U)],layout[activity_download_list(U),activity_fingerprint_settings(U),activity_home(U),header_main(U),content_main(U),texmain(U),footer_main(U),banklay(U),signature_layout(U),activity_invoice_traker(U),activity_invoice_two(U),content_main_two(U),texmain_two(U),activity_main(U),activity_money_bag(U),activity_pdf_viewer(U),activity_selection(U),nav_header(U),activity_varify(U),activity_worker_presentation(U),table(U),table_two(U),custom_file_name_dialog(U),dialog_add_expense(U),dialog_add_income(D),dialog_exit_confirmation(U),dialog_number_to_words(U),dialog_progress(D),dialog_save_pdf(U),dialog_update_item(U),fgdata_upload(U),fragment_data_upload(U),fragment_expense(U),fragment_income(U),item_income(U),fragment_update_delete(U),item(U),item_data_invoice_traker(U),item_expense(U),item_two(U),ivtraker_item_row(D),loading_dialog(U),pdf_item(U),texinfo(U),texinfo_two(U),vertical_border_black(D)],menu[nav_menu(U),text_to_number(U)],mipmap[ic_launcher(U),ic_launcher_round(U),ic_launcher_foreground(U)],raw[loading(U),click_sound(D)],string[app_name(U),to(U),sohar_international(U),po_box_sohar(U),address_sohar(U),vat_no(U),company_name(D),po_box(D),address(D),phone(D),cr_no(D),email(D),quotation(D),subject(D),description(D),date(D),qr_no(D),location(D),vat_in(D),qty(D),uom(D),unit_price(D),amount(D),omr(D),barka(D),mussina(D),suwaiq(D),saham(D),sohar_industrial(D),wadi_hibi(D),sininah(D),burimi(D),mhada(D),total_amount_excluding_vat(D),add_vat(D),total_amount_including_vat(D),total_amount(D),pdf_generated_successfully(U),default_off_text(D),default_on_text(D),hello_blank_fragment(D)],style[Theme_NewInvoice(U),ThemeOverlay_AppCompat_DayNight(R),Theme_AppCompat_NoActionBar(R),Widget_MaterialComponents_TextInputLayout_OutlinedBox(R),Widget_AppCompat_ProgressBar_Horizontal(E),Widget_AppCompat_ImageButton(R),Widget_AppCompat_Button(R),ThemeOverlay_AppCompat_Light(R),TextAppearance_AppCompat_Body1(R),Base_Theme_NewInvoice(U),Theme_Material3_Light_NoActionBar(R)],xml[data_extraction_rules(U),backup_rules(U),network_security_config(U)];14^6,15^7,17^8,24^7,26^25^24,29^7^8,2f^7,50^8,54^8,59^8,5d^8,62^63,f2^55^58^48,f3^37,f4^9^4^151^7^70^6e^2c^f5^f6^f7^f8^f9^fa^57^1e^32^31^5f^20^18^1c^25^24^22^23^5b,f5^8^64,f6^64^128^8^129^12a^12b^12c^9a^99^106,f7^11d,f8^65^8^c6,f9^64^8^29,fb^7^8^84,fc^9^4^151^7^70^6e^2c^f5^fd^fe^f8^f9^57^1e^32^31^5f^20^18^1c^25^24^22^23,fd^64^128^8^129^12a^12b^12c^9a^99^107,fe^11e,ff^52,100^a^84,102^4^1b^7^127^27^51^61^3a^5a^4d^53^103^9^120^152,103^16^157^123^127^158^7,104^35^38^7^8f,106^29^64,107^29^64,108^15^8^153^64^26^7,10a^a5^a8^aa^ab,10b^a^b^c,10c^56^7^5c,10d^154,10e^b^8,10f^65,110^7^8^65^5f,111^4f^55^8^65^155,112^c8^c7,113^114,114^db^da^ab,115^5e^125^58^48^cc,116^29^65,117^21^a^65^8^156,119^29^65,11b^e5,11c^b^7^60^2e^31,11d^29^65,11e^29^65,11f^8,120^7^1f^1a^19^1d,121^2a^4d,122^41^124,123^41^124,150^159,159^15a^e^f^7^10^11^8^9^d^5;;;"/>
    </map>

</incidents>
