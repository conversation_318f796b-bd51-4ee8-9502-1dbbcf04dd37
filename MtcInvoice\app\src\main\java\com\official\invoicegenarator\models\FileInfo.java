package com.official.invoicegenarator.models;

import com.google.gson.annotations.SerializedName;

/**
 * File information model for file management
 * Replaces Firebase Storage metadata
 */
public class FileInfo {
    
    @SerializedName("id")
    private String id;
    
    @SerializedName("original_name")
    private String originalName;
    
    @SerializedName("stored_name")
    private String storedName;
    
    @SerializedName("file_path")
    private String filePath;
    
    @SerializedName("relative_path")
    private String relativePath;
    
    @SerializedName("file_size")
    private long fileSize;
    
    @SerializedName("file_type")
    private String fileType;
    
    @SerializedName("file_extension")
    private String fileExtension;
    
    @SerializedName("file_hash")
    private String fileHash;
    
    @SerializedName("uploaded_by")
    private String uploadedBy;
    
    @SerializedName("uploaded_at")
    private String uploadedAt;
    
    @SerializedName("download_url")
    private String downloadUrl;
    
    // Local fields
    private transient String localPath;
    private transient boolean isDownloaded = false;
    private transient boolean isUploaded = false;
    private transient long downloadTime = 0;
    private transient String syncStatus = "pending";
    
    // Constructors
    public FileInfo() {}
    
    public FileInfo(String originalName, String localPath, long fileSize, String fileType) {
        this.originalName = originalName;
        this.localPath = localPath;
        this.fileSize = fileSize;
        this.fileType = fileType;
        this.fileExtension = getExtensionFromName(originalName);
    }
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getOriginalName() {
        return originalName;
    }
    
    public void setOriginalName(String originalName) {
        this.originalName = originalName;
        this.fileExtension = getExtensionFromName(originalName);
    }
    
    public String getStoredName() {
        return storedName;
    }
    
    public void setStoredName(String storedName) {
        this.storedName = storedName;
    }
    
    public String getFilePath() {
        return filePath;
    }
    
    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }
    
    public String getRelativePath() {
        return relativePath;
    }
    
    public void setRelativePath(String relativePath) {
        this.relativePath = relativePath;
    }
    
    public long getFileSize() {
        return fileSize;
    }
    
    public void setFileSize(long fileSize) {
        this.fileSize = fileSize;
    }
    
    public String getFileType() {
        return fileType;
    }
    
    public void setFileType(String fileType) {
        this.fileType = fileType;
    }
    
    public String getFileExtension() {
        return fileExtension;
    }
    
    public void setFileExtension(String fileExtension) {
        this.fileExtension = fileExtension;
    }
    
    public String getFileHash() {
        return fileHash;
    }
    
    public void setFileHash(String fileHash) {
        this.fileHash = fileHash;
    }
    
    public String getUploadedBy() {
        return uploadedBy;
    }
    
    public void setUploadedBy(String uploadedBy) {
        this.uploadedBy = uploadedBy;
    }
    
    public String getUploadedAt() {
        return uploadedAt;
    }
    
    public void setUploadedAt(String uploadedAt) {
        this.uploadedAt = uploadedAt;
    }
    
    public String getDownloadUrl() {
        return downloadUrl;
    }
    
    public void setDownloadUrl(String downloadUrl) {
        this.downloadUrl = downloadUrl;
    }
    
    public String getLocalPath() {
        return localPath;
    }
    
    public void setLocalPath(String localPath) {
        this.localPath = localPath;
    }
    
    public boolean isDownloaded() {
        return isDownloaded;
    }
    
    public void setDownloaded(boolean downloaded) {
        this.isDownloaded = downloaded;
        if (downloaded) {
            this.downloadTime = System.currentTimeMillis();
        }
    }
    
    public boolean isUploaded() {
        return isUploaded;
    }
    
    public void setUploaded(boolean uploaded) {
        this.isUploaded = uploaded;
    }
    
    public long getDownloadTime() {
        return downloadTime;
    }
    
    public void setDownloadTime(long downloadTime) {
        this.downloadTime = downloadTime;
    }
    
    public String getSyncStatus() {
        return syncStatus;
    }
    
    public void setSyncStatus(String syncStatus) {
        this.syncStatus = syncStatus;
    }
    
    // Helper methods
    private String getExtensionFromName(String fileName) {
        if (fileName == null || !fileName.contains(".")) {
            return "";
        }
        return fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
    }
    
    /**
     * Get formatted file size
     */
    public String getFormattedFileSize() {
        if (fileSize < 1024) {
            return fileSize + " B";
        } else if (fileSize < 1024 * 1024) {
            return String.format("%.1f KB", fileSize / 1024.0);
        } else if (fileSize < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", fileSize / (1024.0 * 1024.0 * 1024.0));
        }
    }
    
    /**
     * Check if file is an image
     */
    public boolean isImage() {
        return fileExtension != null && 
               (fileExtension.equals("jpg") || fileExtension.equals("jpeg") || 
                fileExtension.equals("png") || fileExtension.equals("gif"));
    }
    
    /**
     * Check if file is a PDF
     */
    public boolean isPdf() {
        return "pdf".equals(fileExtension);
    }
    
    /**
     * Check if file is a document
     */
    public boolean isDocument() {
        return fileExtension != null && 
               (fileExtension.equals("doc") || fileExtension.equals("docx") || 
                fileExtension.equals("txt") || fileExtension.equals("rtf"));
    }
    
    /**
     * Get display name for the file
     */
    public String getDisplayName() {
        return originalName != null ? originalName : storedName;
    }
    
    /**
     * Check if file exists locally
     */
    public boolean existsLocally() {
        if (localPath == null) return false;
        java.io.File file = new java.io.File(localPath);
        return file.exists() && file.length() == fileSize;
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        FileInfo fileInfo = (FileInfo) obj;
        return id != null ? id.equals(fileInfo.id) : 
               (originalName != null ? originalName.equals(fileInfo.originalName) : fileInfo.originalName == null);
    }
    
    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 
               (originalName != null ? originalName.hashCode() : 0);
    }
    
    @Override
    public String toString() {
        return "FileInfo{" +
                "id='" + id + '\'' +
                ", originalName='" + originalName + '\'' +
                ", fileSize=" + fileSize +
                ", fileType='" + fileType + '\'' +
                ", isDownloaded=" + isDownloaded +
                ", isUploaded=" + isUploaded +
                '}';
    }
}
