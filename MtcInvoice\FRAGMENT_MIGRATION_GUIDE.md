# Fragment Migration Guide

This guide shows how to migrate your existing fragments from Firebase to the new API backend.

## Overview

The migration involves:
1. Replacing Firebase imports with new API classes
2. Updating DataItem usage to use the new model
3. Replacing Firebase database calls with API service calls
4. Adding local database integration for offline support

## Step 1: Update Imports

### Before (Firebase):
```java
import com.google.firebase.database.DatabaseReference;
import com.google.firebase.database.FirebaseDatabase;
import com.google.firebase.database.ValueEventListener;
import com.google.firebase.database.DataSnapshot;
import com.google.firebase.database.DatabaseError;
```

### After (Custom API):
```java
import com.official.invoicegenarator.network.ApiService;
import com.official.invoicegenarator.network.ApiCallback;
import com.official.invoicegenarator.models.DataItem;
import com.official.invoicegenarator.database.InvoiceDataHelper;
```

## Step 2: Update DataUploadFragment.java

### Current Firebase Implementation:
```java
public class DataUploadFragment extends Fragment {
    private DatabaseReference databaseReference;
    
    // Inner DataItem class (remove this)
    public static class DataItem {
        // ... existing fields
    }
    
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        // Firebase initialization
        databaseReference = FirebaseDatabase.getInstance().getReference("dataItems");
        
        // Upload button click
        uploadButton.setOnClickListener(v -> {
            String id = databaseReference.push().getKey();
            DataItem dataItem = new DataItem(description, location, qr, lpo, inb, amount, w_a, paymentStatus, timestamp);
            
            databaseReference.child(id).setValue(dataItem)
                .addOnSuccessListener(aVoid -> {
                    // Success handling
                })
                .addOnFailureListener(e -> {
                    // Error handling
                });
        });
    }
}
```

### New API Implementation:
```java
public class DataUploadFragment extends Fragment {
    private ApiService apiService;
    private InvoiceDataHelper dbHelper;
    
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        // Initialize API service and database
        apiService = new ApiService();
        dbHelper = InvoiceDataHelper.getInstance(getContext());
        
        // Upload button click
        uploadButton.setOnClickListener(v -> {
            // Create DataItem using the new model
            DataItem dataItem = new DataItem(description, location, qr, lpo, inb, amount, w_a, paymentStatus, System.currentTimeMillis());
            
            // Generate Firebase-style ID
            dataItem.setId(generatePushKey());
            
            // Save locally first (for offline support)
            long localId = dbHelper.insertInvoiceItem(dataItem);
            
            if (localId != -1) {
                // Try to sync with server
                apiService.createInvoiceItem(dataItem, new ApiCallback<DataItem>() {
                    @Override
                    public void onSuccess(DataItem result) {
                        // Mark as synced in local database
                        result.setSynced(true);
                        dbHelper.updateInvoiceItem(result);
                        
                        // Update UI on main thread
                        getActivity().runOnUiThread(() -> {
                            // Success handling
                            Toast.makeText(getContext(), "Data uploaded successfully", Toast.LENGTH_SHORT).show();
                            clearForm();
                        });
                    }
                    
                    @Override
                    public void onError(String error) {
                        // Keep in local database for later sync
                        getActivity().runOnUiThread(() -> {
                            Toast.makeText(getContext(), "Saved locally, will sync when online", Toast.LENGTH_SHORT).show();
                            clearForm();
                        });
                    }
                });
            } else {
                Toast.makeText(getContext(), "Failed to save data", Toast.LENGTH_SHORT).show();
            }
        });
    }
    
    private String generatePushKey() {
        // Generate Firebase-style push key
        String chars = "-0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ_abcdefghijklmnopqrstuvwxyz";
        StringBuilder key = new StringBuilder();
        for (int i = 0; i < 20; i++) {
            key.append(chars.charAt((int) (Math.random() * chars.length())));
        }
        return key.toString();
    }
}
```

## Step 3: Update UpdateDeleteFragment.java

### Current Firebase Implementation:
```java
public class UpdateDeleteFragment extends Fragment {
    private DatabaseReference databaseReference;
    private List<DataItem> dataItemList;
    
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        databaseReference = FirebaseDatabase.getInstance().getReference("dataItems");
        
        // Load data
        databaseReference.addValueEventListener(new ValueEventListener() {
            @Override
            public void onDataChange(@NonNull DataSnapshot dataSnapshot) {
                dataItemList.clear();
                for (DataSnapshot snapshot : dataSnapshot.getChildren()) {
                    DataItem dataItem = snapshot.getValue(DataItem.class);
                    if (dataItem != null) {
                        dataItem.setId(snapshot.getKey());
                        dataItemList.add(dataItem);
                    }
                }
                adapter.notifyDataSetChanged();
            }
            
            @Override
            public void onCancelled(@NonNull DatabaseError databaseError) {
                // Error handling
            }
        });
    }
}
```

### New API Implementation:
```java
public class UpdateDeleteFragment extends Fragment {
    private ApiService apiService;
    private InvoiceDataHelper dbHelper;
    private List<DataItem> dataItemList;
    
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        apiService = new ApiService();
        dbHelper = InvoiceDataHelper.getInstance(getContext());
        
        // Load data (try local first, then sync with server)
        loadData();
    }
    
    private void loadData() {
        // Load from local database first
        List<DataItem> localItems = dbHelper.getAllInvoiceItems();
        dataItemList.clear();
        dataItemList.addAll(localItems);
        adapter.notifyDataSetChanged();
        
        // Then try to sync with server
        apiService.getInvoiceItems(new ApiCallback<List<DataItem>>() {
            @Override
            public void onSuccess(List<DataItem> serverItems) {
                getActivity().runOnUiThread(() -> {
                    // Update local database with server data
                    for (DataItem item : serverItems) {
                        item.setSynced(true);
                        DataItem existingItem = dbHelper.getInvoiceItemById(item.getId());
                        if (existingItem != null) {
                            dbHelper.updateInvoiceItem(item);
                        } else {
                            dbHelper.insertInvoiceItem(item);
                        }
                    }
                    
                    // Refresh UI
                    dataItemList.clear();
                    dataItemList.addAll(dbHelper.getAllInvoiceItems());
                    adapter.notifyDataSetChanged();
                });
            }
            
            @Override
            public void onError(String error) {
                // Continue with local data
                Log.w("UpdateDeleteFragment", "Failed to sync with server: " + error);
            }
        });
    }
    
    private void updateItem(DataItem item) {
        // Update locally first
        dbHelper.updateInvoiceItem(item);
        
        // Try to sync with server
        apiService.updateInvoiceItem(item.getId(), item, new ApiCallback<DataItem>() {
            @Override
            public void onSuccess(DataItem result) {
                result.setSynced(true);
                dbHelper.updateInvoiceItem(result);
                getActivity().runOnUiThread(() -> {
                    Toast.makeText(getContext(), "Updated successfully", Toast.LENGTH_SHORT).show();
                    loadData();
                });
            }
            
            @Override
            public void onError(String error) {
                getActivity().runOnUiThread(() -> {
                    Toast.makeText(getContext(), "Updated locally, will sync when online", Toast.LENGTH_SHORT).show();
                });
            }
        });
    }
    
    private void deleteItem(String itemId) {
        // Soft delete locally
        dbHelper.deleteInvoiceItem(itemId);
        
        // Try to delete on server
        apiService.deleteInvoiceItem(itemId, new ApiCallback<Void>() {
            @Override
            public void onSuccess(Void result) {
                getActivity().runOnUiThread(() -> {
                    Toast.makeText(getContext(), "Deleted successfully", Toast.LENGTH_SHORT).show();
                    loadData();
                });
            }
            
            @Override
            public void onError(String error) {
                getActivity().runOnUiThread(() -> {
                    Toast.makeText(getContext(), "Deleted locally, will sync when online", Toast.LENGTH_SHORT).show();
                    loadData();
                });
            }
        });
    }
}
```

## Step 4: Update File Operations (Home.java, InvoiceTwo.java)

### Current Firebase Storage Implementation:
```java
// Upload to Firebase Storage
FirebaseStorage storage = FirebaseStorage.getInstance();
StorageReference storageRef = storage.getReference("pdfs/" + fileName);
UploadTask uploadTask = storageRef.putFile(pdfUri);

uploadTask.addOnSuccessListener(taskSnapshot -> {
    // Get download URL
    storageRef.getDownloadUrl().addOnSuccessListener(uri -> {
        // Handle success
    });
});
```

### New File API Implementation:
```java
private ApiService apiService;

private void uploadFile(Uri fileUri, String fileName) {
    try {
        // Convert URI to File
        File file = new File(getRealPathFromURI(fileUri));
        
        apiService.uploadFile(file, new ApiCallback<FileInfo>() {
            @Override
            public void onSuccess(FileInfo fileInfo) {
                runOnUiThread(() -> {
                    Toast.makeText(MainActivity.this, "File uploaded successfully", Toast.LENGTH_SHORT).show();
                    // Store file info in local database if needed
                });
            }
            
            @Override
            public void onError(String error) {
                runOnUiThread(() -> {
                    Toast.makeText(MainActivity.this, "Upload failed: " + error, Toast.LENGTH_SHORT).show();
                });
            }
        });
    } catch (Exception e) {
        Toast.makeText(this, "Failed to prepare file for upload", Toast.LENGTH_SHORT).show();
    }
}

private String getRealPathFromURI(Uri contentUri) {
    String[] proj = {MediaStore.Images.Media.DATA};
    CursorLoader loader = new CursorLoader(this, contentUri, proj, null, null, null);
    Cursor cursor = loader.loadInBackground();
    int column_index = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATA);
    cursor.moveToFirst();
    String result = cursor.getString(column_index);
    cursor.close();
    return result;
}
```

## Step 5: Add Sync Service (Optional)

Create a background service to sync pending changes:

```java
public class SyncService extends IntentService {
    private ApiService apiService;
    private InvoiceDataHelper dbHelper;
    
    public SyncService() {
        super("SyncService");
    }
    
    @Override
    protected void onHandleIntent(Intent intent) {
        apiService = new ApiService();
        dbHelper = InvoiceDataHelper.getInstance(this);
        
        // Sync unsynced items
        List<DataItem> unsyncedItems = dbHelper.getUnsyncedItems();
        for (DataItem item : unsyncedItems) {
            syncItem(item);
        }
    }
    
    private void syncItem(DataItem item) {
        if (item.isDeleted()) {
            // Handle deleted items
            apiService.deleteInvoiceItem(item.getId(), new ApiCallback<Void>() {
                @Override
                public void onSuccess(Void result) {
                    // Remove from local database
                }
                
                @Override
                public void onError(String error) {
                    item.markSyncError();
                    dbHelper.updateInvoiceItem(item);
                }
            });
        } else if (item.getId() != null) {
            // Update existing item
            apiService.updateInvoiceItem(item.getId(), item, new ApiCallback<DataItem>() {
                @Override
                public void onSuccess(DataItem result) {
                    result.setSynced(true);
                    dbHelper.updateInvoiceItem(result);
                }
                
                @Override
                public void onError(String error) {
                    item.markSyncError();
                    dbHelper.updateInvoiceItem(item);
                }
            });
        } else {
            // Create new item
            apiService.createInvoiceItem(item, new ApiCallback<DataItem>() {
                @Override
                public void onSuccess(DataItem result) {
                    result.setSynced(true);
                    dbHelper.updateInvoiceItem(result);
                }
                
                @Override
                public void onError(String error) {
                    item.markSyncError();
                    dbHelper.updateInvoiceItem(item);
                }
            });
        }
    }
}
```

## Testing Checklist

After migration:

- [ ] Data upload works and saves locally
- [ ] Data retrieval shows local data immediately
- [ ] Updates work both online and offline
- [ ] Deletes work both online and offline
- [ ] File upload/download works
- [ ] App works without internet connection
- [ ] Data syncs when connection is restored
- [ ] No Firebase dependencies remain
- [ ] All existing UI functionality preserved

## Rollback Plan

Keep Firebase code commented out during migration for easy rollback:

```java
// OLD FIREBASE CODE - KEEP FOR ROLLBACK
/*
databaseReference = FirebaseDatabase.getInstance().getReference("dataItems");
databaseReference.child(id).setValue(dataItem)
    .addOnSuccessListener(aVoid -> {
        // Success handling
    });
*/

// NEW API CODE
apiService.createInvoiceItem(dataItem, new ApiCallback<DataItem>() {
    // Implementation
});
```
