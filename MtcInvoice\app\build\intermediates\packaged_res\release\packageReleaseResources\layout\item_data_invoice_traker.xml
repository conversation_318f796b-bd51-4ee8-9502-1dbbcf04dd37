<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    android:background="@drawable/border_textview"
    android:orientation="vertical"
    android:padding="16dp">
    <!-- TextView for Date and Time -->
    <TextView
        android:id="@+id/text_time_date"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center"
        android:background="@drawable/border_textview"
        android:padding="8dp"
        android:layout_marginBottom="8dp"
        android:text="Date and Time"
        android:textStyle="bold"
        android:textColor="@color/colorAccent"
        android:textSize="19sp" />

    <TextView
        android:id="@+id/text_description"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:background="@drawable/border_textview"
        android:padding="8dp"
        android:textStyle="bold"
        android:fontFamily="@font/timesnewroman"
        android:textColor="@color/black"
        android:text="Description"
        android:textSize="19sp" />

    <TextView
        android:id="@+id/text_location"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:background="@drawable/border_textview"
        android:padding="8dp"
        android:text="Location"
        android:textStyle="bold"
        android:fontFamily="@font/timesnewroman"
        android:textColor="@color/black"
        android:textSize="19sp" />

    <TextView
        android:id="@+id/text_qr"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:background="@drawable/border_textview"
        android:padding="8dp"
        android:text="QR Code"
        android:textStyle="bold"
        android:fontFamily="@font/timesnewroman"
        android:textColor="@color/black"
        android:textSize="19sp" />

    <TextView
        android:id="@+id/text_lpo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:background="@drawable/border_textview"
        android:padding="8dp"
        android:text="LPO"
        android:textStyle="bold"
        android:fontFamily="@font/timesnewroman"
        android:textColor="@color/black"
        android:textSize="19sp" />

    <TextView
        android:id="@+id/text_inb"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:background="@drawable/border_textview"
        android:padding="8dp"
        android:text="Invoice Number"
        android:textStyle="bold"
        android:fontFamily="@font/timesnewroman"
        android:textColor="@color/black"
        android:textSize="19sp" />

    <TextView
        android:id="@+id/text_amount"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:background="@drawable/border_textview"
        android:padding="8dp"
        android:text="Amount"
        android:textStyle="bold"
        android:fontFamily="@font/timesnewroman"
        android:textColor="@color/black"
        android:textSize="19sp" />

    <TextView
        android:id="@+id/text_w_a"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:background="@drawable/border_textview"
        android:padding="8dp"
        android:text="W/A"
        android:textStyle="bold"
        android:fontFamily="@font/timesnewroman"
        android:textColor="@color/black"
        android:textSize="19sp" />

    <TextView
        android:id="@+id/text_payment_status"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:background="@drawable/border_textview"
        android:padding="8dp"
        android:text="Payment Status"
        android:textStyle="bold"
        android:fontFamily="@font/timesnewroman"
        android:textColor="@color/black"
        android:textSize="19sp" />

    <!-- Horizontal layout for buttons and Date/Time -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="8dp"
        android:layout_gravity="center"
        android:gravity="end"
        android:orientation="horizontal">



        <Button
            android:id="@+id/button_edit"
            style="@style/Widget.AppCompat.Button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:layout_gravity="center"
            android:layout_marginEnd="8dp"
            android:text="Edit" />

        <Button
            android:id="@+id/button_delete"
            style="@style/Widget.AppCompat.Button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:layout_gravity="center"
            android:text="Delete" />
    </LinearLayout>


</LinearLayout>
