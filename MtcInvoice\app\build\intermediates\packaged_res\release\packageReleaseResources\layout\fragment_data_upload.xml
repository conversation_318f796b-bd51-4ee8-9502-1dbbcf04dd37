<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@drawable/iv_traker_upload"
    android:padding="16dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="@color/black"
        android:text="Invoice Traker"
        android:background="@drawable/rounded"
        android:textStyle="bold"
        android:gravity="center"
        android:textSize="23sp"
        />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Description">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/editTextDescription"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textStyle="bold"
                    android:fontFamily="@font/timesnewroman"
                    android:textColor="@color/black"
                    android:hint="Description" />
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Location">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/editTextLocation"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textStyle="bold"
                    android:fontFamily="@font/timesnewroman"
                    android:textColor="@color/black"
                    android:hint="Location" />
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="QR">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/editTextQR"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textStyle="bold"
                    android:fontFamily="@font/timesnewroman"
                    android:textColor="@color/black"
                    android:hint="QR" />
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="LPO">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/editTextLPO"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textStyle="bold"
                    android:fontFamily="@font/timesnewroman"
                    android:textColor="@color/black"
                    android:hint="LPO" />
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="INV">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/editTextINB"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textStyle="bold"
                    android:fontFamily="@font/timesnewroman"
                    android:textColor="@color/black"
                    android:hint="INV" />
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Amount">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/editTextAmount"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textStyle="bold"
                    android:fontFamily="@font/timesnewroman"
                    android:textColor="@color/black"
                    android:hint="Amount" />
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="W/A">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/editTextW_A"
                    android:layout_width="match_parent"
                    android:textStyle="bold"
                    android:fontFamily="@font/timesnewroman"
                    android:textColor="@color/black"
                    android:layout_height="wrap_content"
                    android:hint="W/A" />
            </com.google.android.material.textfield.TextInputLayout>
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Payment Status">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/payment_status"
                    android:layout_width="match_parent"
                    android:textStyle="bold"
                    android:fontFamily="@font/timesnewroman"
                    android:textColor="@color/black"
                    android:layout_height="wrap_content"
                    android:hint="Payment Status" />
            </com.google.android.material.textfield.TextInputLayout>
            <Button
                android:id="@+id/buttonUpload"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                style="@style/Widget.AppCompat.ImageButton"
                android:text="Upload Data" />
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>


</LinearLayout>
