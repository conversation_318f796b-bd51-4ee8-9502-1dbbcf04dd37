plugins {
    id 'com.android.application'
    // Add the Google services Gradle plugin
    id 'com.google.gms.google-services'

}
android {
    namespace 'com.official.invoicegenarator'
    compileSdk 35

    defaultConfig {
        applicationId "com.official.invoicegenarator"
        minSdk 23
        targetSdk 35
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    buildTypes {
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            debuggable true
        }


        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            debuggable false
        }
    }

    lintOptions {
        abortOnError false
        checkReleaseBuilds false
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {

    implementation libs.appcompat
    implementation libs.material
    implementation libs.activity
    implementation libs.constraintlayout
    implementation libs.recyclerview
    implementation libs.core
    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core
    implementation 'com.itextpdf:itextpdf:5.5.12'
// add Dexter dependency in  app build.gradle file
    implementation 'com.karumi:dexter:6.2.3'
    // Import the BoM for the Firebase platform
    implementation(platform("com.google.firebase:firebase-bom:33.4.0"))
    implementation 'com.google.firebase:firebase-database:21.0.0' // Firebase Realtime Database
    implementation 'com.google.firebase:firebase-analytics-ktx:22.1.2' // Firebase Analytics (optional)

    // Add the dependency for the Cloud Storage library
    // When using the BoM, you don't specify versions in Firebase library dependencies
    implementation("com.google.firebase:firebase-storage")
    implementation 'androidx.biometric:biometric:1.1.0'
    implementation 'com.google.code.gson:gson:2.10.1'
    implementation 'com.github.zcweng:switch-button:0.0.3@aar'
    //pichart
    implementation 'com.github.PhilJay:MPAndroidChart:v3.1.0'
    implementation 'com.airbnb.android:lottie:6.4.1'
    // ...existing dependencies...
    implementation 'com.github.bumptech.glide:glide:4.12.0'
    annotationProcessor 'com.github.bumptech.glide:compiler:4.12.0'

    // HTTP Client for API communication (replaces Firebase)
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'
    implementation 'com.squareup.okhttp3:okhttp-urlconnection:4.12.0'


}