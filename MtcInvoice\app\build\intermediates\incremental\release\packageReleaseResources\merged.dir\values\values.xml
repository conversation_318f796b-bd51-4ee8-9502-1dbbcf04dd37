<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="black">#FF000000</color>
    <color name="colorAccent">#E93C2F</color>
    <color name="colorPrimary">#1976D2</color>
    <color name="colorPrimaryDark">#3F51B5</color>
    <color name="gray">#4CAF50</color>
    <color name="green">#388E3C</color>
    <color name="orange">#F19802</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="red">#F51504</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="white">#FFFFFFFF</color>
    <string name="add_vat">Add: 5%VAT</string>
    <string name="address">AI-khoudh, Sultanate of Oman</string>
    <string name="address_sohar"><PERSON>, PC 114</string>
    <string name="amount">Amount</string>
    <string name="app_name">MTC Customer Invoice</string>
    <string name="barka">Barka 2 shifting of 8 customers &amp; 8 staff chairs</string>
    <string name="burimi">Burimi 3 staff chairs and 3 tables</string>
    <string name="company_name">MTC Private L.T.D.</string>
    <string name="cr_no">C.R. NO: 1129470</string>
    <string name="date">Date</string>
    <string name="default_off_text">Off</string>
    <string name="default_on_text">On</string>
    <string name="description">Description</string>
    <string name="email"><EMAIL></string>
    <string name="gcm_defaultSenderId" translatable="false">48784707932</string>
    <string name="google_api_key" translatable="false">AIzaSyByDxDKvju37bnMEWOyw9BLD23OVYSqMDo</string>
    <string name="google_app_id" translatable="false">1:48784707932:android:a35b3c2d0531a7237eb199</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyByDxDKvju37bnMEWOyw9BLD23OVYSqMDo</string>
    <string name="google_storage_bucket" translatable="false">mtcinvoice-c3dd6.appspot.com</string>
    <string name="hello_blank_fragment">Hello blank fragment</string>
    <string name="location">Location</string>
    <string name="mhada">Mhada 5 customers</string>
    <string name="mussina">Mussina 2 shifting of 8 chairs staff chairs</string>
    <string name="omr">OMR</string>
    <string name="pdf_generated_successfully">PDF Generated successfully!..</string>
    <string name="phone">Tel: 99535325</string>
    <string name="po_box">P.O. BOX 32, 123</string>
    <string name="po_box_sohar">P.O. Box 44</string>
    <string name="project_id" translatable="false">mtcinvoice-c3dd6</string>
    <string name="qr_no">QR No</string>
    <string name="qty">QTY</string>
    <string name="quotation">QUOTATION</string>
    <string name="saham">Saham 2 shifting of 1 customer &amp; 3 staff chairs</string>
    <string name="sininah">Sininah 2 coatomers &amp; 1 staff chair</string>
    <string name="sohar_industrial">Sohar Industrial shifting of 4 customers &amp; 4 staff chairs</string>
    <string name="sohar_international">Sohar International</string>
    <string name="subject">SUBJECT: shifting of chairs to Batinah Region</string>
    <string name="suwaiq">Suwaiq 2 shifting of 6 staff chairs</string>
    <string name="to">TO:</string>
    <string name="total_amount">Total amount RO 556.5/- (Rial Omani Five Hundred-Fifty-Six &amp; Five Hundred-Baisa-Only)</string>
    <string name="total_amount_excluding_vat">Total Amount Excluding 5% VAT</string>
    <string name="total_amount_including_vat">Total Amount including 5% VAT</string>
    <string name="unit_price">Unit price</string>
    <string name="uom">UOM</string>
    <string name="vat_in">VAT IN</string>
    <string name="vat_no">Sultanate Of Oman</string>
    <string name="wadi_hibi">Wadi Hibi shifting of 4 customers &amp; 4 staff chairs</string>
    <style name="Base.Theme.NewInvoice" parent="Theme.Material3.Light.NoActionBar">
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="android:windowIsTranslucent">true</item>
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">@color/green</item>
        
    </style>
    <style name="Theme.NewInvoice" parent="Base.Theme.NewInvoice"/>
</resources>