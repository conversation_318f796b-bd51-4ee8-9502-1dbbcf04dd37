# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.7.0"
  }
  digests {
    sha256: "g\030\227\023\263\n?\253iqq<\305\372\261\313\177\002+\317d\212%ucq\\\221\327\031\325\204"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.9.2"
  }
  digests {
    sha256: "ru\036X\225\266\375`\017\257>\275\375<\233/\257p\350\321{A<\vf\332\331\214\246K*w"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.6.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.6.0"
  }
  digests {
    sha256: "`\261\v^\365v\233yW\001r\340\025\270\025\224\005\311/\003K\250\213\223\221\251wX\234\235\353N"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "1.9.10"
  }
  digests {
    sha256: "U\351\211\305\022\270\t\ay\237\205C\t\363\274w\202\305\263\32192D-\003y\325\304rq\025\004"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "1.9.10"
  }
  digests {
    sha256: "\315\3434\033\241\212+\242b\260\267\317lU\262\f\220\350\3244\344,\232\023\346\243\367p\333\226Z\210"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.1.0"
  }
  digests {
    sha256: "c*\016T\aF\035\347t@\223R\224\016)*)\0207rB\a\247\207\202\fw\332\367\323;r"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.13.1"
  }
  digests {
    sha256: ",\'\336\031\2255gP\005U0fYzK \372\036\352|\"\212\264\357k2\265\3769\312\037Y"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.4.0"
  }
  digests {
    sha256: "\306\353~g`\021\354e\263$(7=E\r\353\337\304Qy\304\370\263\247R\027O\270|\027\260\212"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "9999.0-empty-to-avoid-conflict-with-guava"
  }
  digests {
    sha256: "\263r\2407\324#\n\245\177\276\377\336\363\017\326\022?\234\f-\270]\n\316\320\f\221\271t\363?\231"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.6.2"
  }
  digests {
    sha256: "Hg\375Ryt/\272\203\210\202\0310\313*\377\340m\201\245(\024\347\344\036p9.\240\357\210|"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.6.2"
  }
  digests {
    sha256: "\363H1\266\307\034\330D\341\323]\033\344\235^yD|Z\270V4e1\261\350go\332st\261"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.7.3"
  }
  digests {
    sha256: "Y\377\373&\276\341,2\332\334\372]B\f*}\270]2SQ\201(\261p\357\332rf\023%m"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.7.3"
  }
  digests {
    sha256: "\032\263\254\303\217>sU\304\371\321\354b\020zF\372s\310\231\363\a\r\005^]Cs\337\346~\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-play-services"
    version: "1.7.3"
  }
  digests {
    sha256: "d\326\352\032M\025\242\300\225\r\251\246\037I\352|Q&\216\347L;\035\335\304c\346\225TD\033$"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-tasks"
    version: "18.2.0"
  }
  digests {
    sha256: "\177*\252\217P h\352\365CV\312\222\256\300Bq\326\347\304\026\305,E\300\3224@\374\275\026T"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-basement"
    version: "18.4.0"
  }
  digests {
    sha256: "\316\\\223o\326h\024\263`/\\j^\222\231\021\377\227=K\005\366\336\231\226\332Yk\357\227\312\322"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.5.4"
  }
  digests {
    sha256: "\274<$1\335\244.\224\273\225\021\305\207\352\350\220\322v\344\252\3769:\215\247\260\001i\030m\257\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.13.1"
  }
  digests {
    sha256: "\031\272P\320\224\3076\216\336\033L\317\021\225\316\270>5\227\a6Y?\202>Z\367\026\370\320]p"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.6.2"
  }
  digests {
    sha256: "\"Vx\n<\377J\036W\373\263\324BU|\027\3346:\270\257\020[\312\365&\035\216-]\271I"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.6.2"
  }
  digests {
    sha256: "g5\237`\235\374+\366]\241\'\v#\003?\205`d\354\'\237\005\216\np\307\025\367\311\00001"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.6.2"
  }
  digests {
    sha256: "\0173\261\275\001\177\226Zj\373.{\363\343\2754<b@a\301\230m\352\213\242F\235\0044\2247"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.0.0"
  }
  digests {
    sha256: "\a\270\266\023\226e\270\204\241b\354\317\227\211\034\245\017\177V\203\0223\277%\026\212\340O{V\206\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.6.2"
  }
  digests {
    sha256: "\344\377C8\231\236\034l\234rG\031\365\324\252}\326\033\366\365E\325%j\'\251\323u\337\237#0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.6.2"
  }
  digests {
    sha256: "{\307\334\272\261v6\354\ao\022\257\344\320&q&\\8\224W\261\263f\263z\016\214\271\036-\240"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.1"
  }
  digests {
    sha256: "!\247\324\274\366\275\271J\327\271(8\001R\223\000\264\373\270\200\214\244\361\221\340\315\316o\330\344pZ"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\3675\313;U\304X\324p\276\331\342RT7[Q\213K\033\255i&x:p&\333\017P%"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.1.0"
  }
  digests {
    sha256: "\001\367j\260Cw\n\227\260T\004o\230\025q{\202\316\003U\300)g\321la\230\023Y\334\030\232"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.9.10"
  }
  digests {
    sha256: "\244\307M\224\326L\341\253\3457`\376\003\211\335\224\037o\305X\320\332\263^G\300\205\241\036\310\017("
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.9.10"
  }
  digests {
    sha256: "\254ca\277\232\321\3558,!\003\331q,G\315\354\026b2\264\220>\325\226\350\207k\006\201\311\267"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.3.1"
  }
  digests {
    sha256: "\320\344\002\3541\362@(\241\334~\266\240\243\371\331c\\\024Y9,\32749cC\267=g9H"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.7.0"
  }
  digests {
    sha256: "U\266w\206\002h\017<(\214\343P\242\302\323\335\025\215\227\333\377\3064v\'X&eU\202\303\210"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.1.1"
  }
  digests {
    sha256: ",_\r\3127\216\267\214\242\304@?\230\211\307}\2520Y0\"`\362j\a\376\237c\300\211&\376"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.3.0"
  }
  digests {
    sha256: "+\3628\030\262:\231m\332\241\265\375[\263!)\332\377k\273-\316\025\026n/\314\335 \020\261\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2-views-helper"
    version: "1.3.0"
  }
  digests {
    sha256: "\232\023Q)ZOs\235\360\357\3504J\332\251\257\263HV\303\257XMJ\232\373\354\020ZE\271\v"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.resourceinspection"
    artifactId: "resourceinspection-annotation"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\377\207\016\306\3731\333H\245/Jy#5\264\277\215\340~\003\2757\2021\201Rd3\314\325\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.material"
    artifactId: "material"
    version: "1.12.0"
  }
  digests {
    sha256: "Jg)A\266&\271\253\221\256\211>\322%\230\352S\255i\022\\\205\214\nY\372\233\220\332\245\313\b"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-bom"
    version: "1.8.22"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.errorprone"
    artifactId: "error_prone_annotations"
    version: "2.26.0"
  }
  digests {
    sha256: "S\315\374\v\353-vo\340;x\360\261\035\002\005T\315A\230y\322\003\200\303\217\241\334\362\272\033P"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.cardview"
    artifactId: "cardview"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\223\300L\"\243\326\265\224m\256\237N\214Y\326\255\336jq\266\275]\207\373\231\330-\332\032\376\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.coordinatorlayout"
    artifactId: "coordinatorlayout"
    version: "1.1.0"
  }
  digests {
    sha256: "D\251\343\n\277V\257\020%\305*\n\365\006\376\351\304\023\032\245^\375\245/\237\331E\022\021\305\350\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout"
    version: "2.1.4"
  }
  digests {
    sha256: "\r\367\024\300\265\036Tq\016\277tn\264i\3233\027k\273<\262\237\200w]\303\312N\263\026%\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout-core"
    version: "1.0.4"
  }
  digests {
    sha256: ">G\177M\3421\345\213%\365\251\222\363\276E\351}3,4\243\232\236>}Kx\256\n\302%o"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.dynamicanimation"
    artifactId: "dynamicanimation"
    version: "1.0.0"
  }
  digests {
    sha256: "\316\000Qb\302)\2770\215-[\022\373l\255\bt\006\234\273\352\314\356c\250\031;\320\215@\336\004"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.0.0"
  }
  digests {
    sha256: "\206Z\006\036\362\372\321e\"\370C56\270\324r\b\304o\367\307tQ\227\337\241\356\264\201\206\224\207"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.0.0"
  }
  digests {
    sha256: "\347\0342\214\356\365\304\247\327o-\206\337\033e\326_\342\254\370h\261\244\357\330J?43a\206\330"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.recyclerview"
    artifactId: "recyclerview"
    version: "1.3.2"
  }
  digests {
    sha256: "\000\\\365\025\020I:$\372H\272\256\032EZRXQS\2215\032qq}\323<\272\225!\031f"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview-poolingcontainer"
    version: "1.0.0"
  }
  digests {
    sha256: "5\204\020/\304\233\363\231\305n;{\344\277\341 \000\304a\0222\f\330\317\205\314\n\217\223\363\347R"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager2"
    artifactId: "viewpager2"
    version: "1.1.0-beta02"
  }
  digests {
    sha256: "\272\372\303\312\231\036\326\212,|\246\3775)f\322\000\261.f\243B\321\017I|\270\026\217Y\005J"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.transition"
    artifactId: "transition"
    version: "1.5.0"
  }
  digests {
    sha256: "\n\246j\016\244\006\322Z\020\221\371j;u;K\022\344O\334C\271\036\305,\027\203\036\2341\365K"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.itextpdf"
    artifactId: "itextpdf"
    version: "5.5.12"
  }
  digests {
    sha256: "\206\221\017\016>\351\263\222\356\016*\254\335o\002\210or\262\006\025\331q\363\356\341\360\241\276\254\234\330"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.karumi"
    artifactId: "dexter"
    version: "6.2.3"
  }
  digests {
    sha256: "Jr\353\210\306F\037,d\'k\217]\316\006\314\347]\265\026\310\006\004D(v\250\025\373Cx\215"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-bom"
    version: "33.4.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-analytics-ktx"
    version: "22.1.2"
  }
  digests {
    sha256: "x\017;D\231\346\220\311]\0356z*\3276\331\257p\277\307\220e\243Z+o\311k\357j\324Q"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-analytics"
    version: "22.1.2"
  }
  digests {
    sha256: "pF\020\375\312\205\373\221^\310?\016-}}\033\222ou\347\365\315G\3332}v\347\320g,J"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement"
    version: "22.1.2"
  }
  digests {
    sha256: "\206\257\312%\266!\002\346O\230\301\242\305\200\365pC\236\205\274\024U\3504\321\355\204\340\337\f\321H"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-ads-identifier"
    version: "18.0.0"
  }
  digests {
    sha256: "s\f\233g\344\370]\2738I\364\363\344*PG\316\341\365\234#&F\235\001\331m\017\275!\030\032"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-base"
    version: "22.1.2"
  }
  digests {
    sha256: "\301~\230\344\177\033\256m\306\037&\177\320M\334\210\031\367\\$\376\026!S\331\220\376\362y\374\211\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-impl"
    version: "22.1.2"
  }
  digests {
    sha256: "\273\3213Sm\256\271\236\302\255un9\023.\3613\360ze\306g+\000tf}\031\346!\316\345"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.privacysandbox.ads"
    artifactId: "ads-adservices"
    version: "1.0.0-beta05"
  }
  digests {
    sha256: "\001\'W\214\334\355\255|\216\316\027H\024\306\364\343\002x\216\217{\321N\203=\221\234\033vY\331}"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.privacysandbox.ads"
    artifactId: "ads-adservices-java"
    version: "1.0.0-beta05"
  }
  digests {
    sha256: "\227\352S+F\274\203\365\254\344\a\342B\247\254\315\330+\204\2407u\331n\257\\\341`\316\275\273\234"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "guava"
    version: "31.1-android"
  }
  digests {
    sha256: "2\254.\327\t\331m\'\213].>\\\352\027\217\244\223\2319\305%\373du2\360\0230\215\263\t"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "failureaccess"
    version: "1.0.1"
  }
  digests {
    sha256: "\241q\356LsM\322\332\203~K\026\276\235\364f\032\372\267*A\255\2571\353\204\337\332\3716\312&"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.findbugs"
    artifactId: "jsr305"
    version: "3.0.2"
  }
  digests {
    sha256: "vj\322\240x?&\207\226,\212\327L\356\3148\242\213\237r\242\320\205\356C\213x\023\351(\320\307"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.checkerframework"
    artifactId: "checker-qual"
    version: "3.12.0"
  }
  digests {
    sha256: "\377\020xZ\302\243W\354]\351\302\223\313\230*,\273`\\\003\t\352L\301\313\233\233\306\333\347\363\313"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.j2objc"
    artifactId: "j2objc-annotations"
    version: "1.3"
  }
  digests {
    sha256: "!\2570\311\"g\275a\"\300\340\264\322\f\314\266d\0327\352\371V\306T\016\304q\325\204\346J{"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-base"
    version: "18.5.0"
  }
  digests {
    sha256: "Y\245\300\302\332\0221\035u\331e\316\037A\224\230Sk\032\026\177\262\217\367\337\302\337\331\316\372AW"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-stats"
    version: "17.0.2"
  }
  digests {
    sha256: "\335C\024\245?I\243x\354\024a\003\323b2\271luEM)Rc6\314\275\3612\224\027d\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-api"
    version: "22.1.2"
  }
  digests {
    sha256: "\020k\351\356\207\201\276\205\037\324\311YJD\020\231\254v\302\377d\'\240\200\237\217\261\365\031}\313\357"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-sdk-api"
    version: "22.1.2"
  }
  digests {
    sha256: "u\'\340\031\205\330\320\351\333\366n\312^\370\243\363\332]\265K;\035aO\263\355\v\352\354X\374\232"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-common"
    version: "21.0.0"
  }
  digests {
    sha256: "7\222\207\327\027\023qQ$\223h\0339\216x\303A\2633\317\227N\350\032\030\261\325n|.8]"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-components"
    version: "18.0.0"
  }
  digests {
    sha256: "\307\304\212:\200\364JI\236\275ds\274\374}\325\244^\365#\372\276$\024\246%\025\377<d\tr"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-annotations"
    version: "16.2.0"
  }
  digests {
    sha256: "F\366\325\337\335,\317<@\336\211z\024\275\227y1L3\031\364K\3751\347\340\242\r\223Z^>"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "javax.inject"
    artifactId: "javax.inject"
    version: "1"
  }
  digests {
    sha256: "\221\307pD\245\fH\0266\303-\221o\330\234\221\030\247!\2259\004R\310\020e\b\017\225}\347\377"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-common-ktx"
    version: "21.0.0"
  }
  digests {
    sha256: "%\374\200\311\273\236\313\026r\220\207\030\302\224\257\314J\301\344tsg{\340`\307\225\340O\022\000f"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-installations"
    version: "18.0.0"
  }
  digests {
    sha256: "\225\006:\337\261\177\376I+\022\366\216s\002\bs\201M\201w\252U0i\312\326N\021\330\307\222\222"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-installations-interop"
    version: "17.1.1"
  }
  digests {
    sha256: "\372\306Ph\017y!\364\253\222\360\273!\240\2224\261,\332\357\253,\367\001\210(\035~\245+\215\255"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-measurement-connector"
    version: "19.0.0"
  }
  digests {
    sha256: "\333\247Mk\371FG\3569{\367\257\262\253\a\366\376\215\023\025~Vx_\245@\242\241>\330,\231"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-sdk"
    version: "22.1.2"
  }
  digests {
    sha256: "\340{r\271)\241\351\017 s+\233\256h\226\240\225t,c\247\226\247\360[\325\336\"A\224IM"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-storage"
    version: "21.0.1"
  }
  digests {
    sha256: "\365\f\3174\217\366\216\241h\346\327\241\372\356g5\370\322q\3461q\333g\350\270\337\315t\342=w"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-appcheck"
    version: "18.0.0"
  }
  digests {
    sha256: "\322z\363\363\034\214\316\241\351\003\f\035\303\204\021\304\v\230g?\027~\3763\344\361\265\021\360\366Y\220"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-appcheck-interop"
    version: "17.1.0"
  }
  digests {
    sha256: "\036\241\020\250\3266\3042`\241\360}zE\372\005H\210Y\372\2637;\023\004/\201\022\005\266\376<"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-auth-interop"
    version: "20.0.0"
  }
  digests {
    sha256: "\300\237\332\337\240WI\315\177]h_\367\237<$\370\273\325+\241\310\033\216k\277M\000\230\301\312I"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-database"
    version: "21.0.0"
  }
  digests {
    sha256: "d\016\ag<\316F[\350;\272\270\337\327bZ9s2\212\377\246V\\\244\331#?\025\260h\247"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-database-collection"
    version: "18.0.1"
  }
  digests {
    sha256: "\373\222`M\363[\370\031\347\006C/\366\343\312\235G\224\314\2054\215\224\310\207b+\251;TP\340"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.biometric"
    artifactId: "biometric"
    version: "1.1.0"
  }
  digests {
    sha256: "\'\f{}\231\224-^\301\335\210YNFH\376\263=\2161\330\303\302\253#!\324\235\232\275\374\037"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.code.gson"
    artifactId: "gson"
    version: "2.10.1"
  }
  digests {
    sha256: "BA\301Jw\'\303O\356\246P~\310\0011\212=J\220\360p\344RV\201\a\237\271N\344\305\223"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.zcweng"
    artifactId: "switch-button"
    version: "0.0.3"
  }
  digests {
    sha256: "6\256\303\f3\004\273\226\201\261\267\023\365\314\304\340&\354M~ 4)\220z\310a\201\266F\220\337"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.PhilJay"
    artifactId: "MPAndroidChart"
    version: "v3.1.0"
  }
  digests {
    sha256: "V\315\021<1\2330\034e\a\216\310\242o/\217g\252N\354\215i\023\311\360\n\326,5C\v\321"
  }
  repo_index {
    value: 3
  }
}
library {
  maven_library {
    groupId: "com.airbnb.android"
    artifactId: "lottie"
    version: "6.4.1"
  }
  digests {
    sha256: "sb\001\202\373,\r\272\344\275\363\033\237n\022\3617\034x[\346\322\362\374k>\3070\220)\344K"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio"
    version: "3.6.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio-jvm"
    version: "3.6.0"
  }
  digests {
    sha256: "gT?\a6\374B*\351\'\355\016PK\230\274^&\237\332\r5\000W\2237\313q=\242\204\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "glide"
    version: "4.12.0"
  }
  digests {
    sha256: "j\342\224J\333b\227\177\345?B\304\370j\"\2752l\030(\266\223D\034\314\3440\351/\024\203\204"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "gifdecoder"
    version: "4.12.0"
  }
  digests {
    sha256: "\031z\034\325\267hU\252\002\2620\3019t\342\223\"\233\220\035\302\271o\253C\025 \036x\272\250\004"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "disklrucache"
    version: "4.12.0"
  }
  digests {
    sha256: "o\f\311\006\226F\254X.\347\215\277b\036\2200>\242\000\271\316A\302\n\303h\351%\355Ecy"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.github.bumptech.glide"
    artifactId: "annotations"
    version: "4.12.0"
  }
  digests {
    sha256: "\372C\f\277\217\223\037\315\211\303\201\225\253UD\357Mb\310\254l.oI\370\364\256x`\372\336\216"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.exifinterface"
    artifactId: "exifinterface"
    version: "1.2.0"
  }
  digests {
    sha256: "\252\346\216Q0\225\344u\247g\005V\352\313\247r\354+\265\222\321q\207\t\025x\323\376\371G\256\247"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "okhttp"
    version: "4.12.0"
  }
  digests {
    sha256: "\261\005\000\201\261K\267\243\247\345ZM>\360\033]\317\253\304S\264W:O\300\031vq\221\325\364\340"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "logging-interceptor"
    version: "4.12.0"
  }
  digests {
    sha256: "\363\350\325\360\220<%\f+U\322\364\177\317\340\b\350\00648]\2508Qa\307\246:\256\320\307L"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "okhttp-urlconnection"
    version: "4.12.0"
  }
  digests {
    sha256: "t\300\343\370\303\337\v\0352\253\235\203\224H\311\024Xm>\204ya\036C\206\376\317\246\263\360\242k"
  }
  repo_index {
    value: 1
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 41
  library_dep_index: 7
  library_dep_index: 8
  library_dep_index: 25
  library_dep_index: 44
  library_dep_index: 45
  library_dep_index: 46
  library_dep_index: 47
  library_dep_index: 24
  library_dep_index: 13
  library_dep_index: 31
  library_dep_index: 39
  library_dep_index: 48
  library_dep_index: 33
  library_dep_index: 4
  library_dep_index: 41
}
library_dependencies {
  library_index: 1
  library_dep_index: 2
  library_dep_index: 7
  library_dep_index: 8
  library_dep_index: 13
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 39
  library_dep_index: 33
  library_dep_index: 30
  library_dep_index: 4
}
library_dependencies {
  library_index: 2
  library_dep_index: 3
}
library_dependencies {
  library_index: 3
  library_dep_index: 4
}
library_dependencies {
  library_index: 4
  library_dep_index: 5
  library_dep_index: 6
}
library_dependencies {
  library_index: 7
  library_dep_index: 2
}
library_dependencies {
  library_index: 8
  library_dep_index: 2
  library_dep_index: 9
  library_dep_index: 7
  library_dep_index: 10
  library_dep_index: 12
  library_dep_index: 13
  library_dep_index: 40
  library_dep_index: 4
  library_dep_index: 25
}
library_dependencies {
  library_index: 9
  library_dep_index: 4
}
library_dependencies {
  library_index: 10
  library_dep_index: 2
  library_dep_index: 11
}
library_dependencies {
  library_index: 12
  library_dep_index: 2
}
library_dependencies {
  library_index: 13
  library_dep_index: 2
  library_dep_index: 14
  library_dep_index: 15
  library_dep_index: 16
  library_dep_index: 39
  library_dep_index: 4
  library_dep_index: 16
  library_dep_index: 26
  library_dep_index: 28
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 27
}
library_dependencies {
  library_index: 14
  library_dep_index: 2
}
library_dependencies {
  library_index: 15
  library_dep_index: 2
  library_dep_index: 14
}
library_dependencies {
  library_index: 16
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 17
  library_dep_index: 27
  library_dep_index: 26
  library_dep_index: 28
  library_dep_index: 13
  library_dep_index: 31
  library_dep_index: 32
}
library_dependencies {
  library_index: 17
  library_dep_index: 18
  library_dep_index: 20
  library_dep_index: 37
}
library_dependencies {
  library_index: 18
  library_dep_index: 19
}
library_dependencies {
  library_index: 19
  library_dep_index: 6
  library_dep_index: 20
  library_dep_index: 5
  library_dep_index: 37
}
library_dependencies {
  library_index: 20
  library_dep_index: 17
  library_dep_index: 19
  library_dep_index: 18
  library_dep_index: 21
}
library_dependencies {
  library_index: 21
  library_dep_index: 18
  library_dep_index: 20
  library_dep_index: 22
  library_dep_index: 37
}
library_dependencies {
  library_index: 22
  library_dep_index: 23
}
library_dependencies {
  library_index: 23
  library_dep_index: 7
  library_dep_index: 8
  library_dep_index: 24
}
library_dependencies {
  library_index: 24
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 9
  library_dep_index: 7
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 34
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 4
}
library_dependencies {
  library_index: 25
  library_dep_index: 2
  library_dep_index: 8
  library_dep_index: 4
  library_dep_index: 8
}
library_dependencies {
  library_index: 26
  library_dep_index: 14
  library_dep_index: 15
  library_dep_index: 16
  library_dep_index: 4
  library_dep_index: 16
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 13
  library_dep_index: 31
  library_dep_index: 32
}
library_dependencies {
  library_index: 27
  library_dep_index: 14
  library_dep_index: 15
  library_dep_index: 26
  library_dep_index: 4
  library_dep_index: 16
  library_dep_index: 26
  library_dep_index: 28
  library_dep_index: 13
  library_dep_index: 31
  library_dep_index: 32
}
library_dependencies {
  library_index: 28
  library_dep_index: 2
  library_dep_index: 13
  library_dep_index: 29
  library_dep_index: 4
  library_dep_index: 16
  library_dep_index: 27
  library_dep_index: 26
  library_dep_index: 13
  library_dep_index: 31
  library_dep_index: 32
}
library_dependencies {
  library_index: 29
  library_dep_index: 2
  library_dep_index: 30
}
library_dependencies {
  library_index: 30
  library_dep_index: 2
}
library_dependencies {
  library_index: 31
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 16
  library_dep_index: 27
  library_dep_index: 26
  library_dep_index: 28
  library_dep_index: 13
  library_dep_index: 32
}
library_dependencies {
  library_index: 32
  library_dep_index: 2
  library_dep_index: 25
  library_dep_index: 26
  library_dep_index: 31
  library_dep_index: 33
  library_dep_index: 4
  library_dep_index: 17
  library_dep_index: 16
  library_dep_index: 27
  library_dep_index: 26
  library_dep_index: 28
  library_dep_index: 13
  library_dep_index: 31
}
library_dependencies {
  library_index: 33
  library_dep_index: 2
  library_dep_index: 14
  library_dep_index: 16
  library_dep_index: 4
}
library_dependencies {
  library_index: 34
  library_dep_index: 2
  library_dep_index: 8
  library_dep_index: 27
  library_dep_index: 31
}
library_dependencies {
  library_index: 35
  library_dep_index: 2
  library_dep_index: 8
  library_dep_index: 36
}
library_dependencies {
  library_index: 36
  library_dep_index: 2
  library_dep_index: 8
  library_dep_index: 7
}
library_dependencies {
  library_index: 37
  library_dep_index: 4
  library_dep_index: 38
}
library_dependencies {
  library_index: 38
  library_dep_index: 4
}
library_dependencies {
  library_index: 39
  library_dep_index: 2
  library_dep_index: 10
  library_dep_index: 29
  library_dep_index: 11
}
library_dependencies {
  library_index: 40
  library_dep_index: 2
  library_dep_index: 7
}
library_dependencies {
  library_index: 41
  library_dep_index: 2
  library_dep_index: 7
  library_dep_index: 8
  library_dep_index: 42
  library_dep_index: 43
  library_dep_index: 0
}
library_dependencies {
  library_index: 42
  library_dep_index: 2
  library_dep_index: 8
  library_dep_index: 7
}
library_dependencies {
  library_index: 43
  library_dep_index: 42
  library_dep_index: 12
  library_dep_index: 7
}
library_dependencies {
  library_index: 44
  library_dep_index: 2
}
library_dependencies {
  library_index: 45
  library_dep_index: 2
  library_dep_index: 8
  library_dep_index: 36
}
library_dependencies {
  library_index: 46
  library_dep_index: 2
  library_dep_index: 7
  library_dep_index: 8
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 47
}
library_dependencies {
  library_index: 47
  library_dep_index: 7
  library_dep_index: 8
  library_dep_index: 46
  library_dep_index: 46
}
library_dependencies {
  library_index: 48
  library_dep_index: 2
}
library_dependencies {
  library_index: 49
  library_dep_index: 50
  library_dep_index: 51
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 0
  library_dep_index: 52
  library_dep_index: 53
  library_dep_index: 54
  library_dep_index: 8
  library_dep_index: 45
  library_dep_index: 56
  library_dep_index: 9
  library_dep_index: 24
  library_dep_index: 13
  library_dep_index: 61
  library_dep_index: 48
  library_dep_index: 64
  library_dep_index: 42
  library_dep_index: 63
}
library_dependencies {
  library_index: 50
  library_dep_index: 4
  library_dep_index: 37
  library_dep_index: 5
  library_dep_index: 38
}
library_dependencies {
  library_index: 52
  library_dep_index: 2
}
library_dependencies {
  library_index: 53
  library_dep_index: 2
  library_dep_index: 8
  library_dep_index: 36
  library_dep_index: 7
}
library_dependencies {
  library_index: 54
  library_dep_index: 0
  library_dep_index: 8
  library_dep_index: 55
}
library_dependencies {
  library_index: 56
  library_dep_index: 8
  library_dep_index: 7
  library_dep_index: 57
}
library_dependencies {
  library_index: 57
  library_dep_index: 2
  library_dep_index: 8
  library_dep_index: 58
  library_dep_index: 34
  library_dep_index: 59
  library_dep_index: 60
}
library_dependencies {
  library_index: 58
  library_dep_index: 2
}
library_dependencies {
  library_index: 59
  library_dep_index: 2
}
library_dependencies {
  library_index: 60
  library_dep_index: 2
}
library_dependencies {
  library_index: 61
  library_dep_index: 2
  library_dep_index: 7
  library_dep_index: 8
  library_dep_index: 36
  library_dep_index: 62
  library_dep_index: 63
}
library_dependencies {
  library_index: 62
  library_dep_index: 25
  library_dep_index: 4
}
library_dependencies {
  library_index: 63
  library_dep_index: 2
  library_dep_index: 9
  library_dep_index: 7
  library_dep_index: 8
  library_dep_index: 24
  library_dep_index: 61
}
library_dependencies {
  library_index: 64
  library_dep_index: 2
  library_dep_index: 7
  library_dep_index: 8
  library_dep_index: 56
}
library_dependencies {
  library_index: 66
  library_dep_index: 0
  library_dep_index: 49
}
library_dependencies {
  library_index: 67
  library_dep_index: 68
  library_dep_index: 94
  library_dep_index: 98
  library_dep_index: 85
  library_dep_index: 89
  library_dep_index: 69
  library_dep_index: 95
  library_dep_index: 90
}
library_dependencies {
  library_index: 68
  library_dep_index: 69
  library_dep_index: 85
  library_dep_index: 89
  library_dep_index: 86
  library_dep_index: 4
}
library_dependencies {
  library_index: 69
  library_dep_index: 70
  library_dep_index: 83
  library_dep_index: 93
}
library_dependencies {
  library_index: 70
  library_dep_index: 7
  library_dep_index: 57
  library_dep_index: 71
  library_dep_index: 23
  library_dep_index: 72
  library_dep_index: 73
  library_dep_index: 82
}
library_dependencies {
  library_index: 71
  library_dep_index: 23
}
library_dependencies {
  library_index: 72
  library_dep_index: 23
}
library_dependencies {
  library_index: 73
  library_dep_index: 7
  library_dep_index: 8
  library_dep_index: 74
  library_dep_index: 75
  library_dep_index: 71
  library_dep_index: 81
  library_dep_index: 23
  library_dep_index: 72
  library_dep_index: 82
  library_dep_index: 22
  library_dep_index: 76
}
library_dependencies {
  library_index: 74
  library_dep_index: 2
  library_dep_index: 25
  library_dep_index: 4
  library_dep_index: 18
  library_dep_index: 75
}
library_dependencies {
  library_index: 75
  library_dep_index: 2
  library_dep_index: 10
  library_dep_index: 25
  library_dep_index: 74
  library_dep_index: 76
  library_dep_index: 11
  library_dep_index: 4
  library_dep_index: 18
  library_dep_index: 74
}
library_dependencies {
  library_index: 76
  library_dep_index: 77
  library_dep_index: 11
  library_dep_index: 78
  library_dep_index: 79
  library_dep_index: 51
  library_dep_index: 80
}
library_dependencies {
  library_index: 81
  library_dep_index: 7
  library_dep_index: 8
  library_dep_index: 24
  library_dep_index: 23
  library_dep_index: 22
}
library_dependencies {
  library_index: 82
  library_dep_index: 57
  library_dep_index: 23
}
library_dependencies {
  library_index: 83
  library_dep_index: 71
  library_dep_index: 23
  library_dep_index: 72
  library_dep_index: 84
  library_dep_index: 22
  library_dep_index: 85
  library_dep_index: 89
  library_dep_index: 86
  library_dep_index: 90
  library_dep_index: 91
  library_dep_index: 92
  library_dep_index: 76
  library_dep_index: 4
}
library_dependencies {
  library_index: 84
  library_dep_index: 23
  library_dep_index: 72
}
library_dependencies {
  library_index: 85
  library_dep_index: 21
  library_dep_index: 86
  library_dep_index: 87
  library_dep_index: 2
  library_dep_index: 10
  library_dep_index: 4
  library_dep_index: 23
  library_dep_index: 22
}
library_dependencies {
  library_index: 86
  library_dep_index: 87
  library_dep_index: 2
  library_dep_index: 51
}
library_dependencies {
  library_index: 87
  library_dep_index: 88
}
library_dependencies {
  library_index: 89
  library_dep_index: 85
  library_dep_index: 37
  library_dep_index: 86
  library_dep_index: 87
}
library_dependencies {
  library_index: 90
  library_dep_index: 22
  library_dep_index: 87
  library_dep_index: 85
  library_dep_index: 89
  library_dep_index: 86
  library_dep_index: 91
  library_dep_index: 4
}
library_dependencies {
  library_index: 91
  library_dep_index: 22
  library_dep_index: 87
}
library_dependencies {
  library_index: 92
  library_dep_index: 23
  library_dep_index: 87
}
library_dependencies {
  library_index: 93
  library_dep_index: 7
  library_dep_index: 23
  library_dep_index: 72
  library_dep_index: 73
}
library_dependencies {
  library_index: 94
  library_dep_index: 87
  library_dep_index: 95
  library_dep_index: 96
  library_dep_index: 97
  library_dep_index: 85
  library_dep_index: 89
  library_dep_index: 86
  library_dep_index: 2
  library_dep_index: 81
  library_dep_index: 22
  library_dep_index: 4
  library_dep_index: 18
}
library_dependencies {
  library_index: 95
  library_dep_index: 22
  library_dep_index: 96
  library_dep_index: 89
  library_dep_index: 2
  library_dep_index: 81
  library_dep_index: 4
}
library_dependencies {
  library_index: 96
  library_dep_index: 81
  library_dep_index: 22
}
library_dependencies {
  library_index: 97
  library_dep_index: 23
  library_dep_index: 22
  library_dep_index: 87
}
library_dependencies {
  library_index: 98
  library_dep_index: 96
  library_dep_index: 85
  library_dep_index: 89
  library_dep_index: 86
  library_dep_index: 97
  library_dep_index: 99
  library_dep_index: 22
  library_dep_index: 2
  library_dep_index: 81
  library_dep_index: 23
  library_dep_index: 4
  library_dep_index: 18
}
library_dependencies {
  library_index: 99
  library_dep_index: 81
}
library_dependencies {
  library_index: 100
  library_dep_index: 1
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 31
  library_dep_index: 2
  library_dep_index: 8
  library_dep_index: 24
}
library_dependencies {
  library_index: 103
  library_dep_index: 2
}
library_dependencies {
  library_index: 104
  library_dep_index: 0
  library_dep_index: 105
}
library_dependencies {
  library_index: 105
  library_dep_index: 106
}
library_dependencies {
  library_index: 106
  library_dep_index: 37
  library_dep_index: 5
}
library_dependencies {
  library_index: 107
  library_dep_index: 108
  library_dep_index: 109
  library_dep_index: 110
  library_dep_index: 24
  library_dep_index: 43
  library_dep_index: 111
}
library_dependencies {
  library_index: 108
  library_dep_index: 2
}
library_dependencies {
  library_index: 111
  library_dep_index: 2
}
library_dependencies {
  library_index: 112
  library_dep_index: 105
  library_dep_index: 37
}
library_dependencies {
  library_index: 113
  library_dep_index: 112
  library_dep_index: 37
}
library_dependencies {
  library_index: 114
  library_dep_index: 112
  library_dep_index: 37
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 49
  dependency_index: 1
  dependency_index: 54
  dependency_index: 61
  dependency_index: 8
  dependency_index: 65
  dependency_index: 66
  dependency_index: 67
  dependency_index: 98
  dependency_index: 68
  dependency_index: 94
  dependency_index: 100
  dependency_index: 101
  dependency_index: 102
  dependency_index: 103
  dependency_index: 104
  dependency_index: 107
  dependency_index: 112
  dependency_index: 113
  dependency_index: 114
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://jcenter.bintray.com"
  }
}
repositories {
  maven_repo {
    url: "https://jitpack.io"
  }
}
