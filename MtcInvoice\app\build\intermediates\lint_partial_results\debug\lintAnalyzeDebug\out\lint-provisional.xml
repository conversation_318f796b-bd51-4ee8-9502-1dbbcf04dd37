<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.1" type="conditional_incidents">

    <incident
        id="ScopedStorage"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="6"
            column="36"
            startOffset="268"
            endLine="6"
            endColumn="77"
            endOffset="309"/>
        <map>
            <entry
                name="maxSdkVersion"
                int="**********"/>
            <entry
                name="read"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="ScopedStorage"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="10"
            column="36"
            startOffset="480"
            endLine="10"
            endColumn="76"
            endOffset="520"/>
        <map>
            <entry
                name="maxSdkVersion"
                int="**********"/>
            <entry
                name="read"
                boolean="true"/>
        </map>
    </incident>

    <incident
        id="InlinedApi"
        severity="warning"
        message="">
        <fix-data minSdk="23-∞" requiresApi="30-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/VarifyActivity.java"
            line="84"
            column="52"
            startOffset="3793"
            endLine="84"
            endColumn="84"
            endOffset="3825"/>
        <map>
            <entry
                name="message"
                string="Field requires API level 30 (current min is %1$s): `android.provider.Settings#ACTION_BIOMETRIC_ENROLL`"/>
            <api-levels id="minSdk"
                value="23-∞"/>
            <entry
                name="name"
                string="ACTION_BIOMETRIC_ENROLL"/>
            <entry
                name="owner"
                string="android.provider.Settings"/>
            <api-levels id="requiresApi"
                value="30-∞"/>
        </map>
    </incident>

    <incident
        id="InlinedApi"
        severity="warning"
        message="">
        <fix-data minSdk="23-∞" requiresApi="30-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/VarifyActivity.java"
            line="85"
            column="35"
            startOffset="3862"
            endLine="85"
            endColumn="82"
            endOffset="3909"/>
        <map>
            <entry
                name="message"
                string="Field requires API level 30 (current min is %1$s): `android.provider.Settings#EXTRA_BIOMETRIC_AUTHENTICATORS_ALLOWED`"/>
            <api-levels id="minSdk"
                value="23-∞"/>
            <entry
                name="name"
                string="EXTRA_BIOMETRIC_AUTHENTICATORS_ALLOWED"/>
            <entry
                name="owner"
                string="android.provider.Settings"/>
            <api-levels id="requiresApi"
                value="30-∞"/>
        </map>
    </incident>

    <incident
        id="UnusedAttribute"
        severity="warning"
        message="">
        <fix-data minSdk="23-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/nav_menu.xml"
            line="5"
            column="9"
            startOffset="214"
            endLine="5"
            endColumn="40"
            endOffset="245"/>
        <map>
            <entry
                name="message"
                string="Attribute `iconTint` is only used in API level 26 and higher (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="23-∞"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="UnusedAttribute"
        severity="warning"
        message="">
        <fix-data minSdk="23-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/nav_menu.xml"
            line="11"
            column="17"
            startOffset="383"
            endLine="11"
            endColumn="48"
            endOffset="414"/>
        <map>
            <entry
                name="message"
                string="Attribute `iconTint` is only used in API level 26 and higher (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="23-∞"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="UnusedAttribute"
        severity="warning"
        message="">
        <fix-data minSdk="23-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/nav_menu.xml"
            line="21"
            column="17"
            startOffset="703"
            endLine="21"
            endColumn="48"
            endOffset="734"/>
        <map>
            <entry
                name="message"
                string="Attribute `iconTint` is only used in API level 26 and higher (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="23-∞"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="UnusedAttribute"
        severity="warning"
        message="">
        <fix-data minSdk="23-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/nav_menu.xml"
            line="31"
            column="17"
            startOffset="1048"
            endLine="31"
            endColumn="48"
            endOffset="1079"/>
        <map>
            <entry
                name="message"
                string="Attribute `iconTint` is only used in API level 26 and higher (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="23-∞"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="UnusedAttribute"
        severity="warning"
        message="">
        <fix-data minSdk="23-∞" requiresApi="26-∞"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/nav_menu.xml"
            line="41"
            column="17"
            startOffset="1380"
            endLine="41"
            endColumn="48"
            endOffset="1411"/>
        <map>
            <entry
                name="message"
                string="Attribute `iconTint` is only used in API level 26 and higher (current min is %1$s)"/>
            <api-levels id="minSdk"
                value="23-∞"/>
            <api-levels id="requiresApi"
                value="26-∞"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/baseline_add_circle_outline_24.xml"
            line="3"
            column="30"
            startOffset="227"
            endLine="3"
            endColumn="50"
            endOffset="247"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/baseline_co_present_24.xml"
            line="3"
            column="30"
            startOffset="227"
            endLine="3"
            endColumn="50"
            endOffset="247"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/baseline_co_present_24.xml"
            line="5"
            column="30"
            startOffset="384"
            endLine="5"
            endColumn="50"
            endOffset="404"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/baseline_co_present_24.xml"
            line="7"
            column="30"
            startOffset="505"
            endLine="7"
            endColumn="50"
            endOffset="525"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/baseline_document_scanner_24.xml"
            line="3"
            column="30"
            startOffset="227"
            endLine="3"
            endColumn="50"
            endOffset="247"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/baseline_menu_24.xml"
            line="3"
            column="30"
            startOffset="227"
            endLine="3"
            endColumn="50"
            endOffset="247"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/baseline_remove_circle_outline_24.xml"
            line="3"
            column="30"
            startOffset="227"
            endLine="3"
            endColumn="50"
            endOffset="247"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/baseline_settings_24.xml"
            line="3"
            column="30"
            startOffset="227"
            endLine="3"
            endColumn="50"
            endOffset="247"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/baseline_settings_backup_restore_24.xml"
            line="3"
            column="30"
            startOffset="227"
            endLine="3"
            endColumn="50"
            endOffset="247"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/baseline_wallet_24.xml"
            line="3"
            column="30"
            startOffset="227"
            endLine="3"
            endColumn="50"
            endOffset="247"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 31">
        <fix-replace
            description="Delete tools:targetApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
                startOffset="1114"
                endOffset="1134"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="23"
            column="9"
            startOffset="1114"
            endLine="23"
            endColumn="29"
            endOffset="1134"/>
        <map>
            <condition minGE="31-∞"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; `SDK_INT` is always >= 21">
        <fix-replace
            description="Delete tools:targetApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/themes.xml"
                startOffset="762"
                endOffset="781"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-night/themes.xml"
            line="14"
            column="45"
            startOffset="762"
            endLine="14"
            endColumn="64"
            endOffset="781"/>
        <map>
            <condition minGE="21-∞"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/banklay.xml"
            line="9"
            column="6"
            startOffset="249"
            endLine="9"
            endColumn="14"
            endOffset="257"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/banklay.xml"
            line="32"
            column="14"
            startOffset="1012"
            endLine="32"
            endColumn="22"
            endOffset="1020"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/banklay.xml"
            line="46"
            column="14"
            startOffset="1581"
            endLine="46"
            endColumn="22"
            endOffset="1589"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/banklay.xml"
            line="65"
            column="14"
            startOffset="2312"
            endLine="65"
            endColumn="22"
            endOffset="2320"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/banklay.xml"
            line="79"
            column="14"
            startOffset="2883"
            endLine="79"
            endColumn="22"
            endOffset="2891"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/banklay.xml"
            line="98"
            column="14"
            startOffset="3574"
            endLine="98"
            endColumn="22"
            endOffset="3582"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/banklay.xml"
            line="112"
            column="14"
            startOffset="4163"
            endLine="112"
            endColumn="22"
            endOffset="4171"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="25"
            column="14"
            startOffset="888"
            endLine="25"
            endColumn="22"
            endOffset="896"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="42"
            column="14"
            startOffset="1501"
            endLine="42"
            endColumn="22"
            endOffset="1509"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="61"
            column="14"
            startOffset="2147"
            endLine="61"
            endColumn="22"
            endOffset="2155"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="79"
            column="14"
            startOffset="2814"
            endLine="79"
            endColumn="22"
            endOffset="2822"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="90"
            column="14"
            startOffset="3286"
            endLine="90"
            endColumn="22"
            endOffset="3294"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="101"
            column="14"
            startOffset="3751"
            endLine="101"
            endColumn="22"
            endOffset="3759"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="131"
            column="14"
            startOffset="4758"
            endLine="131"
            endColumn="22"
            endOffset="4766"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="149"
            column="14"
            startOffset="5416"
            endLine="149"
            endColumn="22"
            endOffset="5424"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="169"
            column="14"
            startOffset="6101"
            endLine="169"
            endColumn="22"
            endOffset="6109"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="188"
            column="14"
            startOffset="6741"
            endLine="188"
            endColumn="22"
            endOffset="6749"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="25"
            column="14"
            startOffset="888"
            endLine="25"
            endColumn="22"
            endOffset="896"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="42"
            column="14"
            startOffset="1501"
            endLine="42"
            endColumn="22"
            endOffset="1509"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="61"
            column="14"
            startOffset="2147"
            endLine="61"
            endColumn="22"
            endOffset="2155"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="79"
            column="14"
            startOffset="2814"
            endLine="79"
            endColumn="22"
            endOffset="2822"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="90"
            column="14"
            startOffset="3286"
            endLine="90"
            endColumn="22"
            endOffset="3294"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="101"
            column="14"
            startOffset="3751"
            endLine="101"
            endColumn="22"
            endOffset="3759"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="131"
            column="14"
            startOffset="4758"
            endLine="131"
            endColumn="22"
            endOffset="4766"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="149"
            column="14"
            startOffset="5416"
            endLine="149"
            endColumn="22"
            endOffset="5424"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="169"
            column="14"
            startOffset="6104"
            endLine="169"
            endColumn="22"
            endOffset="6112"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="188"
            column="14"
            startOffset="6744"
            endLine="188"
            endColumn="22"
            endOffset="6752"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/footer_main.xml"
            line="8"
            column="6"
            startOffset="277"
            endLine="8"
            endColumn="14"
            endOffset="285"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/header_main.xml"
            line="13"
            column="10"
            startOffset="426"
            endLine="13"
            endColumn="18"
            endOffset="434"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/header_main.xml"
            line="23"
            column="10"
            startOffset="821"
            endLine="23"
            endColumn="18"
            endOffset="829"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/header_main.xml"
            line="41"
            column="10"
            startOffset="1449"
            endLine="41"
            endColumn="18"
            endOffset="1457"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/header_main.xml"
            line="51"
            column="10"
            startOffset="1860"
            endLine="51"
            endColumn="18"
            endOffset="1868"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/header_main.xml"
            line="64"
            column="6"
            startOffset="2371"
            endLine="64"
            endColumn="14"
            endOffset="2379"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/header_main.xml"
            line="74"
            column="6"
            startOffset="2728"
            endLine="74"
            endColumn="14"
            endOffset="2736"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item.xml"
            line="11"
            column="10"
            startOffset="349"
            endLine="11"
            endColumn="18"
            endOffset="357"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item.xml"
            line="24"
            column="10"
            startOffset="829"
            endLine="24"
            endColumn="18"
            endOffset="837"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item.xml"
            line="36"
            column="10"
            startOffset="1269"
            endLine="36"
            endColumn="18"
            endOffset="1277"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item.xml"
            line="48"
            column="10"
            startOffset="1715"
            endLine="48"
            endColumn="18"
            endOffset="1723"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item.xml"
            line="60"
            column="10"
            startOffset="2161"
            endLine="60"
            endColumn="18"
            endOffset="2169"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_two.xml"
            line="11"
            column="10"
            startOffset="349"
            endLine="11"
            endColumn="18"
            endOffset="357"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_two.xml"
            line="24"
            column="10"
            startOffset="829"
            endLine="24"
            endColumn="18"
            endOffset="837"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_two.xml"
            line="36"
            column="10"
            startOffset="1269"
            endLine="36"
            endColumn="18"
            endOffset="1277"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_two.xml"
            line="48"
            column="10"
            startOffset="1715"
            endLine="48"
            endColumn="18"
            endOffset="1723"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_two.xml"
            line="60"
            column="10"
            startOffset="2161"
            endLine="60"
            endColumn="18"
            endOffset="2169"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table.xml"
            line="11"
            column="10"
            startOffset="349"
            endLine="11"
            endColumn="18"
            endOffset="357"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table.xml"
            line="26"
            column="10"
            startOffset="876"
            endLine="26"
            endColumn="18"
            endOffset="884"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table.xml"
            line="43"
            column="10"
            startOffset="1483"
            endLine="43"
            endColumn="18"
            endOffset="1491"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table.xml"
            line="57"
            column="10"
            startOffset="1972"
            endLine="57"
            endColumn="18"
            endOffset="1980"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table.xml"
            line="70"
            column="10"
            startOffset="2427"
            endLine="70"
            endColumn="18"
            endOffset="2435"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table_two.xml"
            line="11"
            column="10"
            startOffset="349"
            endLine="11"
            endColumn="18"
            endOffset="357"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table_two.xml"
            line="26"
            column="10"
            startOffset="876"
            endLine="26"
            endColumn="18"
            endOffset="884"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table_two.xml"
            line="43"
            column="10"
            startOffset="1483"
            endLine="43"
            endColumn="18"
            endOffset="1491"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table_two.xml"
            line="57"
            column="10"
            startOffset="1972"
            endLine="57"
            endColumn="18"
            endOffset="1980"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table_two.xml"
            line="70"
            column="10"
            startOffset="2427"
            endLine="70"
            endColumn="18"
            endOffset="2435"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo.xml"
            line="11"
            column="10"
            startOffset="349"
            endLine="11"
            endColumn="18"
            endOffset="357"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo.xml"
            line="25"
            column="10"
            startOffset="857"
            endLine="25"
            endColumn="18"
            endOffset="865"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo.xml"
            line="42"
            column="10"
            startOffset="1511"
            endLine="42"
            endColumn="18"
            endOffset="1519"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo.xml"
            line="55"
            column="10"
            startOffset="1980"
            endLine="55"
            endColumn="18"
            endOffset="1988"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo.xml"
            line="67"
            column="10"
            startOffset="2415"
            endLine="67"
            endColumn="18"
            endOffset="2423"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo_two.xml"
            line="11"
            column="10"
            startOffset="349"
            endLine="11"
            endColumn="18"
            endOffset="357"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo_two.xml"
            line="25"
            column="10"
            startOffset="857"
            endLine="25"
            endColumn="18"
            endOffset="865"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo_two.xml"
            line="42"
            column="10"
            startOffset="1511"
            endLine="42"
            endColumn="18"
            endOffset="1519"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo_two.xml"
            line="55"
            column="10"
            startOffset="1980"
            endLine="55"
            endColumn="18"
            endOffset="1988"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="LabelFor"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo_two.xml"
            line="67"
            column="10"
            startOffset="2415"
            endLine="67"
            endColumn="18"
            endOffset="2423"/>
        <map>
            <entry
                name="hint"
                boolean="false"/>
            <entry
                name="label"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="RtlSymmetry"
        severity="warning"
        message="When you define `paddingEnd` you should probably also define `paddingStart` for right-to-left symmetry">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download_list.xml"
            line="65"
            column="9"
            startOffset="2290"
            endLine="65"
            endColumn="27"
            endOffset="2308"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlSymmetry"
        severity="warning"
        message="When you define `paddingLeft` you should probably also define `paddingRight` for right-to-left symmetry">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="16"
            column="9"
            startOffset="612"
            endLine="16"
            endColumn="28"
            endOffset="631"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlSymmetry"
        severity="warning"
        message="When you define `paddingLeft` you should probably also define `paddingRight` for right-to-left symmetry">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="122"
            column="9"
            startOffset="4486"
            endLine="122"
            endColumn="28"
            endOffset="4505"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlSymmetry"
        severity="warning"
        message="When you define `paddingLeft` you should probably also define `paddingRight` for right-to-left symmetry">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="16"
            column="9"
            startOffset="612"
            endLine="16"
            endColumn="28"
            endOffset="631"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlSymmetry"
        severity="warning"
        message="When you define `paddingLeft` you should probably also define `paddingRight` for right-to-left symmetry">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="122"
            column="9"
            startOffset="4486"
            endLine="122"
            endColumn="28"
            endOffset="4505"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlSymmetry"
        severity="warning"
        message="When you define `paddingLeft` you should probably also define `paddingRight` for right-to-left symmetry">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_save_pdf.xml"
            line="23"
            column="13"
            startOffset="941"
            endLine="23"
            endColumn="32"
            endOffset="960"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:layout_marginRight` with `android:layout_marginEnd=&quot;2dp&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:layout_marginEnd=&quot;2dp&quot;"
            oldString="layout_marginRight"
            replacement="layout_marginEnd"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_selection.xml"
            line="47"
            column="21"
            startOffset="1924"
            endLine="47"
            endColumn="47"
            endOffset="1950"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:layout_marginLeft` with `android:layout_marginStart=&quot;2dp&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:layout_marginStart=&quot;2dp&quot;"
            oldString="layout_marginLeft"
            replacement="layout_marginStart"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_selection.xml"
            line="68"
            column="21"
            startOffset="2892"
            endLine="68"
            endColumn="46"
            endOffset="2917"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:layout_marginRight` with `android:layout_marginEnd=&quot;3dp&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:layout_marginEnd=&quot;3dp&quot;"
            oldString="layout_marginRight"
            replacement="layout_marginEnd"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_selection.xml"
            line="96"
            column="25"
            startOffset="4228"
            endLine="96"
            endColumn="51"
            endOffset="4254"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:paddingLeft` with `android:paddingStart=&quot;6dp&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:paddingStart=&quot;6dp&quot;"
            oldString="paddingLeft"
            replacement="paddingStart"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="16"
            column="9"
            startOffset="612"
            endLine="16"
            endColumn="28"
            endOffset="631"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:paddingLeft` with `android:paddingStart=&quot;6dp&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:paddingStart=&quot;6dp&quot;"
            oldString="paddingLeft"
            replacement="paddingStart"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="122"
            column="9"
            startOffset="4486"
            endLine="122"
            endColumn="28"
            endOffset="4505"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Use &quot;`start`&quot; instead of &quot;`left`&quot; to ensure correct behavior in right-to-left locales">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="219"
            column="30"
            startOffset="7936"
            endLine="219"
            endColumn="34"
            endOffset="7940"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:paddingLeft` with `android:paddingStart=&quot;6dp&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:paddingStart=&quot;6dp&quot;"
            oldString="paddingLeft"
            replacement="paddingStart"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="16"
            column="9"
            startOffset="612"
            endLine="16"
            endColumn="28"
            endOffset="631"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:paddingLeft` with `android:paddingStart=&quot;6dp&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:paddingStart=&quot;6dp&quot;"
            oldString="paddingLeft"
            replacement="paddingStart"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="122"
            column="9"
            startOffset="4486"
            endLine="122"
            endColumn="28"
            endOffset="4505"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Use &quot;`start`&quot; instead of &quot;`left`&quot; to ensure correct behavior in right-to-left locales">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="219"
            column="30"
            startOffset="7939"
            endLine="219"
            endColumn="34"
            endOffset="7943"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:paddingLeft` with `android:paddingStart=&quot;16dp&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:paddingStart=&quot;16dp&quot;"
            oldString="paddingLeft"
            replacement="paddingStart"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_save_pdf.xml"
            line="23"
            column="13"
            startOffset="941"
            endLine="23"
            endColumn="32"
            endOffset="960"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:layout_alignLeft` with `android:layout_alignStart=&quot;@id/edit_text&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:layout_alignStart=&quot;@id/edit_text&quot;"
            oldString="layout_alignLeft"
            replacement="layout_alignStart"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/footer_main.xml"
            line="23"
            column="9"
            startOffset="848"
            endLine="23"
            endColumn="33"
            endOffset="872"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:layout_alignRight` with `android:layout_alignEnd=&quot;@id/edit_text&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:layout_alignEnd=&quot;@id/edit_text&quot;"
            oldString="layout_alignRight"
            replacement="layout_alignEnd"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/footer_main.xml"
            line="24"
            column="9"
            startOffset="898"
            endLine="24"
            endColumn="34"
            endOffset="923"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

</incidents>
