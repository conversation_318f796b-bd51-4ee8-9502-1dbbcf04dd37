<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.1" type="incidents">

    <incident
        id="MissingSuperCall"
        severity="error"
        message="Overriding method should call `super.onBackPressed`">
        <fix-data/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/SelectionActivity.java"
            line="158"
            column="17"
            startOffset="6276"
            endLine="158"
            endColumn="30"
            endOffset="6289"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/DownloadListActivity.java"
            line="209"
            column="38"
            startOffset="8266"
            endLine="209"
            endColumn="49"
            endOffset="8277"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/DownloadListActivity.java"
            line="209"
            column="67"
            startOffset="8295"
            endLine="209"
            endColumn="78"
            endOffset="8306"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/DownloadListActivity.java"
            line="403"
            column="24"
            startOffset="17436"
            endLine="403"
            endColumn="70"
            endOffset="17482"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/DownloadListActivity.java"
            line="405"
            column="24"
            startOffset="17528"
            endLine="405"
            endColumn="73"
            endOffset="17577"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/expense/ExpenseAdapter.java"
            line="35"
            column="33"
            startOffset="1097"
            endLine="35"
            endColumn="74"
            endOffset="1138"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/models/FileInfo.java"
            line="211"
            column="66"
            startOffset="5015"
            endLine="211"
            endColumn="77"
            endOffset="5026"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/models/FileInfo.java"
            line="221"
            column="20"
            startOffset="5262"
            endLine="221"
            endColumn="63"
            endOffset="5305"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/models/FileInfo.java"
            line="223"
            column="20"
            startOffset="5378"
            endLine="223"
            endColumn="74"
            endOffset="5432"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/models/FileInfo.java"
            line="225"
            column="20"
            startOffset="5470"
            endLine="225"
            endColumn="83"
            endOffset="5533"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/Home.java"
            line="830"
            column="66"
            startOffset="33929"
            endLine="830"
            endColumn="97"
            endOffset="33960"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/InvoiceTwo.java"
            line="698"
            column="66"
            startOffset="28552"
            endLine="698"
            endColumn="97"
            endOffset="28583"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/UpdateDeleteFragment.java"
            line="193"
            column="43"
            startOffset="8129"
            endLine="193"
            endColumn="54"
            endOffset="8140"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/UpdateDeleteFragment.java"
            line="193"
            column="72"
            startOffset="8158"
            endLine="193"
            endColumn="83"
            endOffset="8169"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/UpdateDeleteFragment.java"
            line="194"
            column="44"
            startOffset="8219"
            endLine="194"
            endColumn="55"
            endOffset="8230"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/UpdateDeleteFragment.java"
            line="194"
            column="73"
            startOffset="8248"
            endLine="194"
            endColumn="84"
            endOffset="8259"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/UpdateDeleteFragment.java"
            line="195"
            column="38"
            startOffset="8303"
            endLine="195"
            endColumn="49"
            endOffset="8314"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/UpdateDeleteFragment.java"
            line="195"
            column="67"
            startOffset="8332"
            endLine="195"
            endColumn="78"
            endOffset="8343"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/UpdateDeleteFragment.java"
            line="196"
            column="39"
            startOffset="8388"
            endLine="196"
            endColumn="50"
            endOffset="8399"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/UpdateDeleteFragment.java"
            line="196"
            column="68"
            startOffset="8417"
            endLine="196"
            endColumn="79"
            endOffset="8428"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/UpdateDeleteFragment.java"
            line="197"
            column="39"
            startOffset="8473"
            endLine="197"
            endColumn="50"
            endOffset="8484"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/UpdateDeleteFragment.java"
            line="197"
            column="68"
            startOffset="8502"
            endLine="197"
            endColumn="79"
            endOffset="8513"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/UpdateDeleteFragment.java"
            line="198"
            column="51"
            startOffset="8570"
            endLine="198"
            endColumn="62"
            endOffset="8581"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/UpdateDeleteFragment.java"
            line="198"
            column="80"
            startOffset="8599"
            endLine="198"
            endColumn="91"
            endOffset="8610"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/UpdateDeleteFragment.java"
            line="199"
            column="42"
            startOffset="8658"
            endLine="199"
            endColumn="53"
            endOffset="8669"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/UpdateDeleteFragment.java"
            line="199"
            column="71"
            startOffset="8687"
            endLine="199"
            endColumn="82"
            endOffset="8698"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/UpdateDeleteFragment.java"
            line="200"
            column="49"
            startOffset="8753"
            endLine="200"
            endColumn="60"
            endOffset="8764"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/UpdateDeleteFragment.java"
            line="200"
            column="78"
            startOffset="8782"
            endLine="200"
            endColumn="89"
            endOffset="8793"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/UpdateDeleteFragment.java"
            line="201"
            column="39"
            startOffset="8838"
            endLine="201"
            endColumn="50"
            endOffset="8849"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/UpdateDeleteFragment.java"
            line="201"
            column="68"
            startOffset="8867"
            endLine="201"
            endColumn="79"
            endOffset="8878"/>
    </incident>

    <incident
        id="DuplicateIncludedIds"
        severity="warning"
        message="Duplicate id @+id/desc, defined or included multiple times in layout/activity_home.xml: [layout/activity_home.xml => layout/texmain.xml => layout/texinfo.xml defines @+id/desc, layout/activity_home.xml => layout/content_main.xml => layout/table.xml defines @+id/desc]">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_home.xml"
            line="48"
            column="13"
            startOffset="1825"
            endLine="48"
            endColumn="49"
            endOffset="1861"
            message="Duplicate id @+id/desc, defined or included multiple times in layout/activity_home.xml: [layout/activity_home.xml => layout/texmain.xml => layout/texinfo.xml defines @+id/desc, layout/activity_home.xml => layout/content_main.xml => layout/table.xml defines @+id/desc]"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo.xml"
            line="26"
            column="13"
            startOffset="879"
            endLine="26"
            endColumn="35"
            endOffset="901"
            message="Defined here, included via layout/activity_home.xml => layout/texmain.xml => layout/texinfo.xml defines @+id/desc"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table.xml"
            line="27"
            column="13"
            startOffset="898"
            endLine="27"
            endColumn="35"
            endOffset="920"
            message="Defined here, included via layout/activity_home.xml => layout/content_main.xml => layout/table.xml defines @+id/desc"/>
    </incident>

    <incident
        id="DuplicateIncludedIds"
        severity="warning"
        message="Duplicate id @+id/serial, defined or included multiple times in layout/activity_home.xml: [layout/activity_home.xml => layout/texmain.xml => layout/texinfo.xml defines @+id/serial, layout/activity_home.xml => layout/content_main.xml => layout/table.xml defines @+id/serial]">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_home.xml"
            line="48"
            column="13"
            startOffset="1825"
            endLine="48"
            endColumn="49"
            endOffset="1861"
            message="Duplicate id @+id/serial, defined or included multiple times in layout/activity_home.xml: [layout/activity_home.xml => layout/texmain.xml => layout/texinfo.xml defines @+id/serial, layout/activity_home.xml => layout/content_main.xml => layout/table.xml defines @+id/serial]"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo.xml"
            line="12"
            column="13"
            startOffset="371"
            endLine="12"
            endColumn="37"
            endOffset="395"
            message="Defined here, included via layout/activity_home.xml => layout/texmain.xml => layout/texinfo.xml defines @+id/serial"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table.xml"
            line="12"
            column="13"
            startOffset="371"
            endLine="12"
            endColumn="37"
            endOffset="395"
            message="Defined here, included via layout/activity_home.xml => layout/content_main.xml => layout/table.xml defines @+id/serial"/>
    </incident>

    <incident
        id="DuplicateIncludedIds"
        severity="warning"
        message="Duplicate id @+id/desc, defined or included multiple times in layout/activity_invoice_two.xml: [layout/activity_invoice_two.xml => layout/texmain_two.xml => layout/texinfo_two.xml defines @+id/desc, layout/activity_invoice_two.xml => layout/content_main_two.xml => layout/table_two.xml defines @+id/desc]">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_invoice_two.xml"
            line="48"
            column="13"
            startOffset="1836"
            endLine="48"
            endColumn="53"
            endOffset="1876"
            message="Duplicate id @+id/desc, defined or included multiple times in layout/activity_invoice_two.xml: [layout/activity_invoice_two.xml => layout/texmain_two.xml => layout/texinfo_two.xml defines @+id/desc, layout/activity_invoice_two.xml => layout/content_main_two.xml => layout/table_two.xml defines @+id/desc]"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo_two.xml"
            line="26"
            column="13"
            startOffset="879"
            endLine="26"
            endColumn="35"
            endOffset="901"
            message="Defined here, included via layout/activity_invoice_two.xml => layout/texmain_two.xml => layout/texinfo_two.xml defines @+id/desc"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table_two.xml"
            line="27"
            column="13"
            startOffset="898"
            endLine="27"
            endColumn="35"
            endOffset="920"
            message="Defined here, included via layout/activity_invoice_two.xml => layout/content_main_two.xml => layout/table_two.xml defines @+id/desc"/>
    </incident>

    <incident
        id="DuplicateIncludedIds"
        severity="warning"
        message="Duplicate id @+id/serial, defined or included multiple times in layout/activity_invoice_two.xml: [layout/activity_invoice_two.xml => layout/texmain_two.xml => layout/texinfo_two.xml defines @+id/serial, layout/activity_invoice_two.xml => layout/content_main_two.xml => layout/table_two.xml defines @+id/serial]">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_invoice_two.xml"
            line="48"
            column="13"
            startOffset="1836"
            endLine="48"
            endColumn="53"
            endOffset="1876"
            message="Duplicate id @+id/serial, defined or included multiple times in layout/activity_invoice_two.xml: [layout/activity_invoice_two.xml => layout/texmain_two.xml => layout/texinfo_two.xml defines @+id/serial, layout/activity_invoice_two.xml => layout/content_main_two.xml => layout/table_two.xml defines @+id/serial]"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo_two.xml"
            line="12"
            column="13"
            startOffset="371"
            endLine="12"
            endColumn="37"
            endOffset="395"
            message="Defined here, included via layout/activity_invoice_two.xml => layout/texmain_two.xml => layout/texinfo_two.xml defines @+id/serial"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table_two.xml"
            line="12"
            column="13"
            startOffset="371"
            endLine="12"
            endColumn="37"
            endOffset="395"
            message="Defined here, included via layout/activity_invoice_two.xml => layout/content_main_two.xml => layout/table_two.xml defines @+id/serial"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/expense/DatabaseHelper.java"
            line="89"
            column="40"
            startOffset="3293"
            endLine="89"
            endColumn="72"
            endOffset="3325"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/expense/DatabaseHelper.java"
            line="90"
            column="44"
            startOffset="3371"
            endLine="90"
            endColumn="80"
            endOffset="3407"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/expense/DatabaseHelper.java"
            line="91"
            column="52"
            startOffset="3461"
            endLine="91"
            endColumn="90"
            endOffset="3499"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/expense/DatabaseHelper.java"
            line="92"
            column="48"
            startOffset="3549"
            endLine="92"
            endColumn="82"
            endOffset="3583"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/expense/DatabaseHelper.java"
            line="93"
            column="48"
            startOffset="3633"
            endLine="93"
            endColumn="82"
            endOffset="3667"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/expense/DatabaseHelper.java"
            line="94"
            column="48"
            startOffset="3717"
            endLine="94"
            endColumn="82"
            endOffset="3751"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/expense/DatabaseHelper.java"
            line="120"
            column="40"
            startOffset="4555"
            endLine="120"
            endColumn="72"
            endOffset="4587"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/expense/DatabaseHelper.java"
            line="121"
            column="44"
            startOffset="4633"
            endLine="121"
            endColumn="80"
            endOffset="4669"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/expense/DatabaseHelper.java"
            line="122"
            column="52"
            startOffset="4723"
            endLine="122"
            endColumn="90"
            endOffset="4761"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/expense/DatabaseHelper.java"
            line="123"
            column="48"
            startOffset="4811"
            endLine="123"
            endColumn="82"
            endOffset="4845"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/expense/DatabaseHelper.java"
            line="124"
            column="48"
            startOffset="4895"
            endLine="124"
            endColumn="82"
            endOffset="4929"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/expense/DatabaseHelper.java"
            line="125"
            column="48"
            startOffset="4979"
            endLine="125"
            endColumn="82"
            endOffset="5013"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/expense/ExpenseFragment.java"
            line="69"
            column="40"
            startOffset="2365"
            endLine="69"
            endColumn="67"
            endOffset="2392"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/expense/ExpenseFragment.java"
            line="70"
            column="52"
            startOffset="2472"
            endLine="70"
            endColumn="85"
            endOffset="2505"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/expense/ExpenseFragment.java"
            line="71"
            column="44"
            startOffset="2551"
            endLine="71"
            endColumn="75"
            endOffset="2582"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/expense/ExpenseFragment.java"
            line="74"
            column="48"
            startOffset="2802"
            endLine="74"
            endColumn="77"
            endOffset="2831"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/expense/ExpenseFragment.java"
            line="91"
            column="52"
            startOffset="3459"
            endLine="91"
            endColumn="85"
            endOffset="3492"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/expense/ExpenseFragment.java"
            line="92"
            column="44"
            startOffset="3538"
            endLine="92"
            endColumn="75"
            endOffset="3569"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/database/InvoiceDataHelper.java"
            line="327"
            column="42"
            startOffset="13074"
            endLine="327"
            endColumn="80"
            endOffset="13112"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/database/InvoiceDataHelper.java"
            line="328"
            column="37"
            startOffset="13152"
            endLine="328"
            endColumn="69"
            endOffset="13184"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/database/InvoiceDataHelper.java"
            line="329"
            column="46"
            startOffset="13233"
            endLine="329"
            endColumn="87"
            endOffset="13274"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/database/InvoiceDataHelper.java"
            line="330"
            column="43"
            startOffset="13320"
            endLine="330"
            endColumn="81"
            endOffset="13358"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/database/InvoiceDataHelper.java"
            line="331"
            column="37"
            startOffset="13398"
            endLine="331"
            endColumn="69"
            endOffset="13430"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/database/InvoiceDataHelper.java"
            line="332"
            column="38"
            startOffset="13471"
            endLine="332"
            endColumn="71"
            endOffset="13504"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/database/InvoiceDataHelper.java"
            line="333"
            column="38"
            startOffset="13545"
            endLine="333"
            endColumn="71"
            endOffset="13578"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/database/InvoiceDataHelper.java"
            line="334"
            column="41"
            startOffset="13622"
            endLine="334"
            endColumn="77"
            endOffset="13658"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/database/InvoiceDataHelper.java"
            line="335"
            column="38"
            startOffset="13699"
            endLine="335"
            endColumn="71"
            endOffset="13732"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/database/InvoiceDataHelper.java"
            line="336"
            column="48"
            startOffset="13783"
            endLine="336"
            endColumn="92"
            endOffset="13827"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/database/InvoiceDataHelper.java"
            line="337"
            column="42"
            startOffset="13872"
            endLine="337"
            endColumn="81"
            endOffset="13911"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/database/InvoiceDataHelper.java"
            line="338"
            column="50"
            startOffset="13964"
            endLine="338"
            endColumn="97"
            endOffset="14011"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/database/InvoiceDataHelper.java"
            line="339"
            column="38"
            startOffset="14052"
            endLine="339"
            endColumn="77"
            endOffset="14091"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/database/InvoiceDataHelper.java"
            line="340"
            column="45"
            startOffset="14144"
            endLine="340"
            endColumn="89"
            endOffset="14188"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/database/InvoiceDataHelper.java"
            line="341"
            column="45"
            startOffset="14236"
            endLine="341"
            endColumn="86"
            endOffset="14277"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/database/InvoiceDataHelper.java"
            line="342"
            column="39"
            startOffset="14319"
            endLine="342"
            endColumn="79"
            endOffset="14359"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/database/InvoiceDataHelper.java"
            line="343"
            column="42"
            startOffset="14409"
            endLine="343"
            endColumn="82"
            endOffset="14449"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/database/InvoiceDataHelper.java"
            line="344"
            column="42"
            startOffset="14494"
            endLine="344"
            endColumn="82"
            endOffset="14534"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/database/InvoiceDataHelper.java"
            line="414"
            column="41"
            startOffset="16900"
            endLine="414"
            endColumn="71"
            endOffset="16930"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/database/InvoiceDataHelper.java"
            line="415"
            column="51"
            startOffset="16984"
            endLine="415"
            endColumn="92"
            endOffset="17025"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/database/InvoiceDataHelper.java"
            line="416"
            column="49"
            startOffset="17077"
            endLine="416"
            endColumn="88"
            endOffset="17116"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/database/InvoiceDataHelper.java"
            line="417"
            column="48"
            startOffset="17167"
            endLine="417"
            endColumn="86"
            endOffset="17205"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/database/InvoiceDataHelper.java"
            line="418"
            column="51"
            startOffset="17259"
            endLine="418"
            endColumn="90"
            endOffset="17298"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/database/InvoiceDataHelper.java"
            line="419"
            column="45"
            startOffset="17346"
            endLine="419"
            endColumn="77"
            endOffset="17378"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/database/InvoiceDataHelper.java"
            line="420"
            column="47"
            startOffset="17428"
            endLine="420"
            endColumn="79"
            endOffset="17460"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/database/InvoiceDataHelper.java"
            line="421"
            column="52"
            startOffset="17515"
            endLine="421"
            endColumn="89"
            endOffset="17552"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/database/InvoiceDataHelper.java"
            line="422"
            column="47"
            startOffset="17602"
            endLine="422"
            endColumn="79"
            endOffset="17634"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/database/InvoiceDataHelper.java"
            line="423"
            column="46"
            startOffset="17683"
            endLine="423"
            endColumn="87"
            endOffset="17724"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/database/InvoiceDataHelper.java"
            line="424"
            column="44"
            startOffset="17776"
            endLine="424"
            endColumn="83"
            endOffset="17815"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/database/InvoiceDataHelper.java"
            line="425"
            column="49"
            startOffset="17872"
            endLine="425"
            endColumn="90"
            endOffset="17913"/>
    </incident>

    <incident
        id="Range"
        severity="error"
        message="Value must be ≥ 0 but `getColumnIndex` can be -1">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/database/InvoiceDataHelper.java"
            line="426"
            column="49"
            startOffset="17965"
            endLine="426"
            endColumn="88"
            endOffset="18004"/>
    </incident>

    <incident
        id="InflateParams"
        severity="warning"
        message="Avoid passing `null` as the view root (needed to resolve layout parameters on the inflated layout&apos;s root element)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/Home.java"
            line="628"
            column="60"
            startOffset="24725"
            endLine="628"
            endColumn="64"
            endOffset="24729"/>
    </incident>

    <incident
        id="InflateParams"
        severity="warning"
        message="Avoid passing `null` as the view root (needed to resolve layout parameters on the inflated layout&apos;s root element)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/Home.java"
            line="651"
            column="63"
            startOffset="25946"
            endLine="651"
            endColumn="67"
            endOffset="25950"/>
    </incident>

    <incident
        id="InflateParams"
        severity="warning"
        message="Avoid passing `null` as the view root (needed to resolve layout parameters on the inflated layout&apos;s root element)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/InvoiceTwo.java"
            line="523"
            column="64"
            startOffset="20602"
            endLine="523"
            endColumn="68"
            endOffset="20606"/>
    </incident>

    <incident
        id="InflateParams"
        severity="warning"
        message="Avoid passing `null` as the view root (needed to resolve layout parameters on the inflated layout&apos;s root element)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/InvoiceTwo.java"
            line="546"
            column="67"
            startOffset="21827"
            endLine="546"
            endColumn="71"
            endOffset="21831"/>
    </incident>

    <incident
        id="MissingInflatedId"
        severity="error"
        message="`@layout/dialog_add_expense` does not contain a declaration with id `spCategory`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/expense/ExpenseFragment.java"
            line="112"
            column="54"
            startOffset="4329"
            endLine="112"
            endColumn="69"
            endOffset="4344"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/download.xml"
            line="2"
            column="20"
            startOffset="87"
            endLine="2"
            endColumn="25"
            endOffset="92"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/downloadlist.xml"
            line="2"
            column="20"
            startOffset="87"
            endLine="2"
            endColumn="25"
            endOffset="92"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/upload.xml"
            line="2"
            column="20"
            startOffset="87"
            endLine="2"
            endColumn="25"
            endOffset="92"/>
    </incident>

    <incident
        id="AndroidGradlePluginVersion"
        severity="warning"
        message="A newer version of com.android.application than 8.5.2 is available: 8.10.1">
        <fix-replace
            description="Change to 8.10.1"
            family="Update versions"
            oldString="8.5.2"
            replacement="8.10.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="2"
            column="7"
            startOffset="17"
            endLine="2"
            endColumn="14"
            endOffset="24"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.firebase:firebase-bom than 33.4.0 is available: 33.15.0">
        <fix-replace
            description="Change to 33.15.0"
            family="Update versions"
            oldString="33.4.0"
            replacement="33.15.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="55"
            column="20"
            startOffset="1556"
            endLine="55"
            endColumn="71"
            endOffset="1607"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.firebase:firebase-analytics-ktx than 22.1.2 is available: 22.4.0">
        <fix-replace
            description="Change to 22.4.0"
            family="Update versions"
            oldString="22.1.2"
            replacement="22.4.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="57"
            column="20"
            startOffset="1724"
            endLine="57"
            endColumn="71"
            endOffset="1775"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.code.gson:gson than 2.10.1 is available: 2.11.0">
        <fix-replace
            description="Change to 2.11.0"
            family="Update versions"
            oldString="2.10.1"
            replacement="2.11.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="63"
            column="20"
            startOffset="2087"
            endLine="63"
            endColumn="54"
            endOffset="2121"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.airbnb.android:lottie than 6.4.1 is available: 6.6.1">
        <fix-replace
            description="Change to 6.6.1"
            family="Update versions"
            oldString="6.4.1"
            replacement="6.6.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="67"
            column="20"
            startOffset="2280"
            endLine="67"
            endColumn="53"
            endOffset="2313"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.github.bumptech.glide:glide than 4.12.0 is available: 4.16.0">
        <fix-replace
            description="Change to 4.16.0"
            family="Update versions"
            oldString="4.12.0"
            replacement="4.16.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="69"
            column="20"
            startOffset="2368"
            endLine="69"
            endColumn="60"
            endOffset="2408"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.github.bumptech.glide:compiler than 4.12.0 is available: 4.16.0">
        <fix-replace
            description="Change to 4.16.0"
            family="Update versions"
            oldString="4.12.0"
            replacement="4.16.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="70"
            column="25"
            startOffset="2433"
            endLine="70"
            endColumn="68"
            endOffset="2476"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.appcompat:appcompat than 1.7.0 is available: 1.7.1">
        <fix-replace
            description="Change to 1.7.1"
            family="Update versions"
            oldString="1.7.0"
            replacement="1.7.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="7"
            column="13"
            startOffset="101"
            endLine="7"
            endColumn="20"
            endOffset="108"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.activity:activity than 1.9.2 is available: 1.10.1">
        <fix-replace
            description="Change to 1.10.1"
            family="Update versions"
            oldString="1.9.2"
            replacement="1.10.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="9"
            column="12"
            startOffset="140"
            endLine="9"
            endColumn="19"
            endOffset="147"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.constraintlayout:constraintlayout than 2.1.4 is available: 2.2.1">
        <fix-replace
            description="Change to 2.2.1"
            family="Update versions"
            oldString="2.1.4"
            replacement="2.2.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="10"
            column="20"
            startOffset="167"
            endLine="10"
            endColumn="27"
            endOffset="174"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.recyclerview:recyclerview than 1.3.2 is available: 1.4.0">
        <fix-replace
            description="Change to 1.4.0"
            family="Update versions"
            oldString="1.3.2"
            replacement="1.4.0"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="11"
            column="16"
            startOffset="190"
            endLine="11"
            endColumn="23"
            endOffset="197"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.core:core than 1.13.1 is available: 1.16.0">
        <fix-replace
            description="Change to 1.16.0"
            family="Update versions"
            oldString="1.13.1"
            replacement="1.16.0"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="12"
            column="8"
            startOffset="205"
            endLine="12"
            endColumn="16"
            endOffset="213"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/DownloadListActivity.java"
            line="163"
            column="17"
            startOffset="6436"
            endLine="163"
            endColumn="50"
            endOffset="6469"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/DownloadListActivity.java"
            line="214"
            column="9"
            startOffset="8412"
            endLine="214"
            endColumn="42"
            endOffset="8445"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/expense/ExpenseFragment.java"
            line="81"
            column="9"
            startOffset="3121"
            endLine="81"
            endColumn="46"
            endOffset="3158"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/expense/IncomeAdapter.java"
            line="45"
            column="9"
            startOffset="1396"
            endLine="45"
            endColumn="31"
            endOffset="1418"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/UpdateDeleteFragment.java"
            line="170"
            column="17"
            startOffset="7193"
            endLine="170"
            endColumn="51"
            endOffset="7227"/>
    </incident>

    <incident
        id="NotifyDataSetChanged"
        severity="warning"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/UpdateDeleteFragment.java"
            line="345"
            column="9"
            startOffset="15399"
            endLine="345"
            endColumn="31"
            endOffset="15421"/>
    </incident>

    <incident
        id="ObsoleteLayoutParam"
        severity="warning"
        message="Invalid layout param &apos;`layout_centerInParent`&apos; (included from within a `LinearLayout` in `layout/activity_home.xml`, included from within a `LinearLayout` in `layout/activity_invoice_two.xml`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/footer_main.xml"
            line="5"
            column="5"
            startOffset="203"
            endLine="5"
            endColumn="41"
            endOffset="239"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (904 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/baseline_settings_24.xml"
            line="3"
            column="70"
            startOffset="267"
            endLine="3"
            endColumn="974"
            endOffset="1171"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (1033 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/downloadlist.xml"
            line="7"
            column="25"
            startOffset="224"
            endLine="7"
            endColumn="1058"
            endOffset="1257"/>
    </incident>

    <incident
        id="VectorPath"
        severity="warning"
        message="Very long vector path (848 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_accounts.xml"
            line="3"
            column="81"
            startOffset="250"
            endLine="3"
            endColumn="929"
            endOffset="1098"/>
    </incident>

    <incident
        id="InefficientWeight"
        severity="warning"
        message="Use a `layout_width` of `0dp` instead of `34dp` for better performance">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_home.xml"
            line="231"
            column="17"
            startOffset="8730"
            endLine="231"
            endColumn="44"
            endOffset="8757"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `#2f3640` with a theme that also paints a background (inferred theme is `@style/Theme.NewInvoice`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download_list.xml"
            line="5"
            column="5"
            startOffset="197"
            endLine="5"
            endColumn="33"
            endOffset="225"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@drawable/fingerprintsetting` with a theme that also paints a background (inferred theme is `@style/Theme.NewInvoice`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_fingerprint_settings.xml"
            line="5"
            column="5"
            startOffset="197"
            endLine="5"
            endColumn="54"
            endOffset="246"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@drawable/mtcsplash` with a theme that also paints a background (inferred theme is `@style/Theme.NewInvoice`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="7"
            column="5"
            startOffset="341"
            endLine="7"
            endColumn="45"
            endOffset="381"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@drawable/fingerback` with a theme that also paints a background (inferred theme is `@style/Theme.NewInvoice`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_varify.xml"
            line="5"
            column="5"
            startOffset="260"
            endLine="5"
            endColumn="46"
            endOffset="301"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/white` with a theme that also paints a background (inferred theme is `@style/Theme.NewInvoice`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fgdata_upload.xml"
            line="12"
            column="5"
            startOffset="486"
            endLine="12"
            endColumn="38"
            endOffset="519"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@drawable/iv_traker_upload` with a theme that also paints a background (inferred theme is `@style/Theme.NewInvoice`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_data_upload.xml"
            line="7"
            column="5"
            startOffset="294"
            endLine="7"
            endColumn="52"
            endOffset="341"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@drawable/update_delete_iv_traker` with a theme that also paints a background (inferred theme is `@style/Theme.NewInvoice`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_update_delete.xml"
            line="5"
            column="5"
            startOffset="220"
            endLine="5"
            endColumn="59"
            endOffset="274"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@android:color/white` with a theme that also paints a background (inferred theme is `@style/Theme.NewInvoice`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/loading_dialog.xml"
            line="6"
            column="5"
            startOffset="295"
            endLine="6"
            endColumn="46"
            endOffset="336"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `RadioGroup` layout or its `LinearLayout` parent is possibly unnecessary; transfer the `background` attribute to the other view">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download_list.xml"
            line="15"
            column="10"
            startOffset="543"
            endLine="15"
            endColumn="20"
            endOffset="553"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `LinearLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="221"
            column="14"
            startOffset="7959"
            endLine="221"
            endColumn="26"
            endOffset="7971"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `LinearLayout` layout or its `LinearLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="221"
            column="14"
            startOffset="7962"
            endLine="221"
            endColumn="26"
            endOffset="7974"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `TableRow` layout or its `TableLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item.xml"
            line="7"
            column="6"
            startOffset="236"
            endLine="7"
            endColumn="14"
            endOffset="244"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `TableRow` layout or its `TableLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_two.xml"
            line="7"
            column="6"
            startOffset="236"
            endLine="7"
            endColumn="14"
            endOffset="244"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `LinearLayout` layout or its `LinearLayout` parent is unnecessary; transfer the `background` attribute to the other view">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/nav_header.xml"
            line="11"
            column="6"
            startOffset="408"
            endLine="11"
            endColumn="18"
            endOffset="420"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `LinearLayout` layout or its `RelativeLayout` parent is unnecessary; transfer the `background` attribute to the other view">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/pdf_item.xml"
            line="17"
            column="10"
            startOffset="621"
            endLine="17"
            endColumn="22"
            endOffset="633"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `TableRow` layout or its `TableLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table.xml"
            line="7"
            column="6"
            startOffset="236"
            endLine="7"
            endColumn="14"
            endOffset="244"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `TableRow` layout or its `TableLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table_two.xml"
            line="7"
            column="6"
            startOffset="236"
            endLine="7"
            endColumn="14"
            endOffset="244"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `TableRow` layout or its `TableLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo.xml"
            line="7"
            column="6"
            startOffset="236"
            endLine="7"
            endColumn="14"
            endOffset="244"/>
    </incident>

    <incident
        id="UselessParent"
        severity="warning"
        message="This `TableRow` layout or its `TableLayout` parent is unnecessary">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo_two.xml"
            line="7"
            column="6"
            startOffset="236"
            endLine="7"
            endColumn="14"
            endOffset="244"/>
    </incident>

    <incident
        id="IconColors"
        severity="warning"
        message="Action Bar icons should use a single gray color (`#333333` for light themes (with 60%/30% opacity for enabled/disabled), and `#FFFFFF` with opacity 80%/30% for dark themes">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/converter_document.png"/>
    </incident>

    <incident
        id="IconColors"
        severity="warning"
        message="Action Bar icons should use a single gray color (`#333333` for light themes (with 60%/30% opacity for enabled/disabled), and `#FFFFFF` with opacity 80%/30% for dark themes">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/invoice_traker.png"/>
    </incident>

    <incident
        id="MonochromeLauncherIcon"
        severity="warning"
        message="The application adaptive icon is missing a monochrome tag">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="5"
            endColumn="17"
            endOffset="272"/>
    </incident>

    <incident
        id="MonochromeLauncherIcon"
        severity="warning"
        message="The application adaptive roundIcon is missing a monochrome tag">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/mipmap-anydpi-v26/ic_launcher_round.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="5"
            endColumn="17"
            endOffset="272"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/converter_document.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/converter_document.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/delete.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/delete.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/empty_state.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/empty_state.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/fingerback.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/fingerback.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/fingerprint.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/fingerprint.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/fingerprintsetting.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/fingerprintsetting.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/fingerprintthree.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/fingerprintthree.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/fingerprinttwo.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/fingerprinttwo.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/first_invoice.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/first_invoice.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/ic_business.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_business.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/ic_investment.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_investment.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/ic_loan.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_loan.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/ic_other.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_other.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/ic_rent.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_rent.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/ic_salary.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_salary.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/img.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/img.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/invoice.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/invoice.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/invoice_traker.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/invoice_traker.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/invoicelogo.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/invoicelogo.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/iv_traker_upload.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/iv_traker_upload.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/moneybag.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/moneybag.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/mtcsplash.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/mtcsplash.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/navigationicon.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/navigationicon.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/second_invoice.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/second_invoice.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/signature.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/signature.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/update_delete_iv_traker.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/update_delete_iv_traker.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/view.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/view.png"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/workerattendance.png` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/workerattendance.png"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/custom_file_name_dialog.xml"
                    startOffset="1752"
                    endOffset="2728"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/custom_file_name_dialog.xml"
                    startOffset="1952"
                    endOffset="2324"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/custom_file_name_dialog.xml"
                    startOffset="2336"
                    endOffset="2705"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/custom_file_name_dialog.xml"
            line="49"
            column="10"
            startOffset="1953"
            endLine="49"
            endColumn="16"
            endOffset="1959"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/custom_file_name_dialog.xml"
                    startOffset="1752"
                    endOffset="2728"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/custom_file_name_dialog.xml"
                    startOffset="1952"
                    endOffset="2324"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/custom_file_name_dialog.xml"
                    startOffset="2336"
                    endOffset="2705"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/custom_file_name_dialog.xml"
            line="59"
            column="10"
            startOffset="2337"
            endLine="59"
            endColumn="16"
            endOffset="2343"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_exit_confirmation.xml"
                    startOffset="1516"
                    endOffset="2301"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_exit_confirmation.xml"
                    startOffset="1767"
                    endOffset="2020"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_exit_confirmation.xml"
                    startOffset="2036"
                    endOffset="2276"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_exit_confirmation.xml"
            line="47"
            column="14"
            startOffset="1768"
            endLine="47"
            endColumn="20"
            endOffset="1774"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_exit_confirmation.xml"
                    startOffset="1516"
                    endOffset="2301"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_exit_confirmation.xml"
                    startOffset="1767"
                    endOffset="2020"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_exit_confirmation.xml"
                    startOffset="2036"
                    endOffset="2276"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_exit_confirmation.xml"
            line="54"
            column="14"
            startOffset="2037"
            endLine="54"
            endColumn="20"
            endOffset="2043"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/ivtraker_item_row.xml"
                    startOffset="2257"
                    endOffset="3068"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/ivtraker_item_row.xml"
                    startOffset="2482"
                    endOffset="2750"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/ivtraker_item_row.xml"
                    startOffset="2762"
                    endOffset="3047"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/ivtraker_item_row.xml"
            line="80"
            column="10"
            startOffset="2483"
            endLine="80"
            endColumn="16"
            endOffset="2489"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/ivtraker_item_row.xml"
                    startOffset="2257"
                    endOffset="3068"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/ivtraker_item_row.xml"
                    startOffset="2482"
                    endOffset="2750"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/ivtraker_item_row.xml"
                    startOffset="2762"
                    endOffset="3047"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/ivtraker_item_row.xml"
            line="88"
            column="10"
            startOffset="2763"
            endLine="88"
            endColumn="16"
            endOffset="2769"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/banklay.xml"
            line="9"
            column="6"
            startOffset="249"
            endLine="9"
            endColumn="14"
            endOffset="257"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/banklay.xml"
            line="32"
            column="14"
            startOffset="1012"
            endLine="32"
            endColumn="22"
            endOffset="1020"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/banklay.xml"
            line="46"
            column="14"
            startOffset="1581"
            endLine="46"
            endColumn="22"
            endOffset="1589"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/banklay.xml"
            line="65"
            column="14"
            startOffset="2312"
            endLine="65"
            endColumn="22"
            endOffset="2320"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/banklay.xml"
            line="79"
            column="14"
            startOffset="2883"
            endLine="79"
            endColumn="22"
            endOffset="2891"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/banklay.xml"
            line="98"
            column="14"
            startOffset="3574"
            endLine="98"
            endColumn="22"
            endOffset="3582"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/banklay.xml"
            line="112"
            column="14"
            startOffset="4163"
            endLine="112"
            endColumn="22"
            endOffset="4171"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="25"
            column="14"
            startOffset="888"
            endLine="25"
            endColumn="22"
            endOffset="896"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="42"
            column="14"
            startOffset="1501"
            endLine="42"
            endColumn="22"
            endOffset="1509"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="61"
            column="14"
            startOffset="2147"
            endLine="61"
            endColumn="22"
            endOffset="2155"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="79"
            column="14"
            startOffset="2814"
            endLine="79"
            endColumn="22"
            endOffset="2822"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="90"
            column="14"
            startOffset="3286"
            endLine="90"
            endColumn="22"
            endOffset="3294"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="101"
            column="14"
            startOffset="3751"
            endLine="101"
            endColumn="22"
            endOffset="3759"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="131"
            column="14"
            startOffset="4758"
            endLine="131"
            endColumn="22"
            endOffset="4766"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="149"
            column="14"
            startOffset="5416"
            endLine="149"
            endColumn="22"
            endOffset="5424"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="169"
            column="14"
            startOffset="6101"
            endLine="169"
            endColumn="22"
            endOffset="6109"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="188"
            column="14"
            startOffset="6741"
            endLine="188"
            endColumn="22"
            endOffset="6749"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="226"
            column="18"
            startOffset="8148"
            endLine="226"
            endColumn="26"
            endOffset="8156"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="25"
            column="14"
            startOffset="888"
            endLine="25"
            endColumn="22"
            endOffset="896"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="42"
            column="14"
            startOffset="1501"
            endLine="42"
            endColumn="22"
            endOffset="1509"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="61"
            column="14"
            startOffset="2147"
            endLine="61"
            endColumn="22"
            endOffset="2155"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="79"
            column="14"
            startOffset="2814"
            endLine="79"
            endColumn="22"
            endOffset="2822"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="90"
            column="14"
            startOffset="3286"
            endLine="90"
            endColumn="22"
            endOffset="3294"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="101"
            column="14"
            startOffset="3751"
            endLine="101"
            endColumn="22"
            endOffset="3759"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="131"
            column="14"
            startOffset="4758"
            endLine="131"
            endColumn="22"
            endOffset="4766"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="149"
            column="14"
            startOffset="5416"
            endLine="149"
            endColumn="22"
            endOffset="5424"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="169"
            column="14"
            startOffset="6104"
            endLine="169"
            endColumn="22"
            endOffset="6112"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="188"
            column="14"
            startOffset="6744"
            endLine="188"
            endColumn="22"
            endOffset="6752"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="226"
            column="18"
            startOffset="8151"
            endLine="226"
            endColumn="26"
            endOffset="8159"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_add_expense.xml"
            line="15"
            column="6"
            startOffset="490"
            endLine="15"
            endColumn="14"
            endOffset="498"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_add_income.xml"
            line="19"
            column="6"
            startOffset="741"
            endLine="19"
            endColumn="14"
            endOffset="749"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/footer_main.xml"
            line="8"
            column="6"
            startOffset="277"
            endLine="8"
            endColumn="14"
            endOffset="285"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/header_main.xml"
            line="13"
            column="10"
            startOffset="426"
            endLine="13"
            endColumn="18"
            endOffset="434"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/header_main.xml"
            line="23"
            column="10"
            startOffset="821"
            endLine="23"
            endColumn="18"
            endOffset="829"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/header_main.xml"
            line="41"
            column="10"
            startOffset="1449"
            endLine="41"
            endColumn="18"
            endOffset="1457"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/header_main.xml"
            line="51"
            column="10"
            startOffset="1860"
            endLine="51"
            endColumn="18"
            endOffset="1868"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/header_main.xml"
            line="64"
            column="6"
            startOffset="2371"
            endLine="64"
            endColumn="14"
            endOffset="2379"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/header_main.xml"
            line="74"
            column="6"
            startOffset="2728"
            endLine="74"
            endColumn="14"
            endOffset="2736"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item.xml"
            line="11"
            column="10"
            startOffset="349"
            endLine="11"
            endColumn="18"
            endOffset="357"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item.xml"
            line="24"
            column="10"
            startOffset="829"
            endLine="24"
            endColumn="18"
            endOffset="837"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item.xml"
            line="36"
            column="10"
            startOffset="1269"
            endLine="36"
            endColumn="18"
            endOffset="1277"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item.xml"
            line="48"
            column="10"
            startOffset="1715"
            endLine="48"
            endColumn="18"
            endOffset="1723"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item.xml"
            line="60"
            column="10"
            startOffset="2161"
            endLine="60"
            endColumn="18"
            endOffset="2169"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_two.xml"
            line="11"
            column="10"
            startOffset="349"
            endLine="11"
            endColumn="18"
            endOffset="357"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_two.xml"
            line="24"
            column="10"
            startOffset="829"
            endLine="24"
            endColumn="18"
            endOffset="837"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_two.xml"
            line="36"
            column="10"
            startOffset="1269"
            endLine="36"
            endColumn="18"
            endOffset="1277"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_two.xml"
            line="48"
            column="10"
            startOffset="1715"
            endLine="48"
            endColumn="18"
            endOffset="1723"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_two.xml"
            line="60"
            column="10"
            startOffset="2161"
            endLine="60"
            endColumn="18"
            endOffset="2169"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table.xml"
            line="11"
            column="10"
            startOffset="349"
            endLine="11"
            endColumn="18"
            endOffset="357"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table.xml"
            line="26"
            column="10"
            startOffset="876"
            endLine="26"
            endColumn="18"
            endOffset="884"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table.xml"
            line="43"
            column="10"
            startOffset="1483"
            endLine="43"
            endColumn="18"
            endOffset="1491"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table.xml"
            line="57"
            column="10"
            startOffset="1972"
            endLine="57"
            endColumn="18"
            endOffset="1980"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table.xml"
            line="70"
            column="10"
            startOffset="2427"
            endLine="70"
            endColumn="18"
            endOffset="2435"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table_two.xml"
            line="11"
            column="10"
            startOffset="349"
            endLine="11"
            endColumn="18"
            endOffset="357"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table_two.xml"
            line="26"
            column="10"
            startOffset="876"
            endLine="26"
            endColumn="18"
            endOffset="884"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table_two.xml"
            line="43"
            column="10"
            startOffset="1483"
            endLine="43"
            endColumn="18"
            endOffset="1491"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table_two.xml"
            line="57"
            column="10"
            startOffset="1972"
            endLine="57"
            endColumn="18"
            endOffset="1980"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table_two.xml"
            line="70"
            column="10"
            startOffset="2427"
            endLine="70"
            endColumn="18"
            endOffset="2435"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo.xml"
            line="11"
            column="10"
            startOffset="349"
            endLine="11"
            endColumn="18"
            endOffset="357"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo.xml"
            line="25"
            column="10"
            startOffset="857"
            endLine="25"
            endColumn="18"
            endOffset="865"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo.xml"
            line="42"
            column="10"
            startOffset="1511"
            endLine="42"
            endColumn="18"
            endOffset="1519"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo.xml"
            line="55"
            column="10"
            startOffset="1980"
            endLine="55"
            endColumn="18"
            endOffset="1988"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo.xml"
            line="67"
            column="10"
            startOffset="2415"
            endLine="67"
            endColumn="18"
            endOffset="2423"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo_two.xml"
            line="11"
            column="10"
            startOffset="349"
            endLine="11"
            endColumn="18"
            endOffset="357"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo_two.xml"
            line="25"
            column="10"
            startOffset="857"
            endLine="25"
            endColumn="18"
            endOffset="865"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo_two.xml"
            line="42"
            column="10"
            startOffset="1511"
            endLine="42"
            endColumn="18"
            endOffset="1519"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo_two.xml"
            line="55"
            column="10"
            startOffset="1980"
            endLine="55"
            endColumn="18"
            endOffset="1988"/>
    </incident>

    <incident
        id="TextFields"
        severity="warning"
        message="This text field does not specify an `inputType`">
        <fix-attribute
            description="Set inputType"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="inputType"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo_two.xml"
            line="67"
            column="10"
            startOffset="2415"
            endLine="67"
            endColumn="18"
            endOffset="2423"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `8sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="33"
            column="17"
            startOffset="1265"
            endLine="33"
            endColumn="39"
            endOffset="1287"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `8sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="50"
            column="17"
            startOffset="1895"
            endLine="50"
            endColumn="39"
            endOffset="1917"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `8sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="69"
            column="17"
            startOffset="2534"
            endLine="69"
            endColumn="39"
            endOffset="2556"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `8sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="87"
            column="17"
            startOffset="3202"
            endLine="87"
            endColumn="39"
            endOffset="3224"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `8sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="98"
            column="17"
            startOffset="3667"
            endLine="98"
            endColumn="39"
            endOffset="3689"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `8sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="109"
            column="17"
            startOffset="4138"
            endLine="109"
            endColumn="39"
            endOffset="4160"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `8sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="140"
            column="17"
            startOffset="5180"
            endLine="140"
            endColumn="39"
            endOffset="5202"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `8sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="158"
            column="17"
            startOffset="5849"
            endLine="158"
            endColumn="39"
            endOffset="5871"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `8sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="177"
            column="17"
            startOffset="6489"
            endLine="177"
            endColumn="39"
            endOffset="6511"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `8sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="196"
            column="17"
            startOffset="7129"
            endLine="196"
            endColumn="39"
            endOffset="7151"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `10sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="234"
            column="21"
            startOffset="8560"
            endLine="234"
            endColumn="44"
            endOffset="8583"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `8sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="33"
            column="17"
            startOffset="1265"
            endLine="33"
            endColumn="39"
            endOffset="1287"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `8sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="50"
            column="17"
            startOffset="1895"
            endLine="50"
            endColumn="39"
            endOffset="1917"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `8sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="69"
            column="17"
            startOffset="2534"
            endLine="69"
            endColumn="39"
            endOffset="2556"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `8sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="87"
            column="17"
            startOffset="3202"
            endLine="87"
            endColumn="39"
            endOffset="3224"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `8sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="98"
            column="17"
            startOffset="3667"
            endLine="98"
            endColumn="39"
            endOffset="3689"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `8sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="109"
            column="17"
            startOffset="4138"
            endLine="109"
            endColumn="39"
            endOffset="4160"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `8sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="140"
            column="17"
            startOffset="5180"
            endLine="140"
            endColumn="39"
            endOffset="5202"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `8sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="158"
            column="17"
            startOffset="5852"
            endLine="158"
            endColumn="39"
            endOffset="5874"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `8sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="177"
            column="17"
            startOffset="6492"
            endLine="177"
            endColumn="39"
            endOffset="6514"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `8sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="196"
            column="17"
            startOffset="7132"
            endLine="196"
            endColumn="39"
            endOffset="7154"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `10sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="234"
            column="21"
            startOffset="8563"
            endLine="234"
            endColumn="44"
            endOffset="8586"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `10sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/footer_main.xml"
            line="17"
            column="9"
            startOffset="683"
            endLine="17"
            endColumn="32"
            endOffset="706"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `10sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/header_main.xml"
            line="19"
            column="13"
            startOffset="704"
            endLine="19"
            endColumn="36"
            endOffset="727"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `10sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/header_main.xml"
            line="30"
            column="13"
            startOffset="1156"
            endLine="30"
            endColumn="36"
            endOffset="1179"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `8sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/header_main.xml"
            line="47"
            column="13"
            startOffset="1737"
            endLine="47"
            endColumn="35"
            endOffset="1759"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `8sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/header_main.xml"
            line="58"
            column="13"
            startOffset="2229"
            endLine="58"
            endColumn="35"
            endOffset="2251"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `9sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/header_main.xml"
            line="71"
            column="9"
            startOffset="2658"
            endLine="71"
            endColumn="31"
            endOffset="2680"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `9sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item.xml"
            line="22"
            column="13"
            startOffset="792"
            endLine="22"
            endColumn="35"
            endOffset="814"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `9sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item.xml"
            line="34"
            column="13"
            startOffset="1232"
            endLine="34"
            endColumn="35"
            endOffset="1254"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `9sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item.xml"
            line="46"
            column="13"
            startOffset="1678"
            endLine="46"
            endColumn="35"
            endOffset="1700"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `9sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item.xml"
            line="58"
            column="13"
            startOffset="2124"
            endLine="58"
            endColumn="35"
            endOffset="2146"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `9sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item.xml"
            line="71"
            column="13"
            startOffset="2613"
            endLine="71"
            endColumn="35"
            endOffset="2635"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `9sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_two.xml"
            line="22"
            column="13"
            startOffset="792"
            endLine="22"
            endColumn="35"
            endOffset="814"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `9sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_two.xml"
            line="34"
            column="13"
            startOffset="1232"
            endLine="34"
            endColumn="35"
            endOffset="1254"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `9sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_two.xml"
            line="46"
            column="13"
            startOffset="1678"
            endLine="46"
            endColumn="35"
            endOffset="1700"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `9sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_two.xml"
            line="58"
            column="13"
            startOffset="2124"
            endLine="58"
            endColumn="35"
            endOffset="2146"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `9sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_two.xml"
            line="71"
            column="13"
            startOffset="2613"
            endLine="71"
            endColumn="35"
            endOffset="2635"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `9sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table.xml"
            line="22"
            column="13"
            startOffset="787"
            endLine="22"
            endColumn="35"
            endOffset="809"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `9sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table.xml"
            line="40"
            column="13"
            startOffset="1432"
            endLine="40"
            endColumn="35"
            endOffset="1454"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `9sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table.xml"
            line="53"
            column="13"
            startOffset="1883"
            endLine="53"
            endColumn="35"
            endOffset="1905"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `9sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table.xml"
            line="66"
            column="13"
            startOffset="2338"
            endLine="66"
            endColumn="35"
            endOffset="2360"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `9sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table.xml"
            line="80"
            column="13"
            startOffset="2830"
            endLine="80"
            endColumn="35"
            endOffset="2852"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `9sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table_two.xml"
            line="22"
            column="13"
            startOffset="787"
            endLine="22"
            endColumn="35"
            endOffset="809"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `9sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table_two.xml"
            line="40"
            column="13"
            startOffset="1432"
            endLine="40"
            endColumn="35"
            endOffset="1454"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `9sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table_two.xml"
            line="53"
            column="13"
            startOffset="1883"
            endLine="53"
            endColumn="35"
            endOffset="1905"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `9sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table_two.xml"
            line="66"
            column="13"
            startOffset="2338"
            endLine="66"
            endColumn="35"
            endOffset="2360"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `9sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table_two.xml"
            line="80"
            column="13"
            startOffset="2830"
            endLine="80"
            endColumn="35"
            endOffset="2852"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `10sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo.xml"
            line="22"
            column="13"
            startOffset="780"
            endLine="22"
            endColumn="36"
            endOffset="803"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `10sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo.xml"
            line="40"
            column="13"
            startOffset="1473"
            endLine="40"
            endColumn="36"
            endOffset="1496"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `10sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo.xml"
            line="53"
            column="13"
            startOffset="1942"
            endLine="53"
            endColumn="36"
            endOffset="1965"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `10sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo.xml"
            line="65"
            column="13"
            startOffset="2377"
            endLine="65"
            endColumn="36"
            endOffset="2400"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `10sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo.xml"
            line="78"
            column="13"
            startOffset="2846"
            endLine="78"
            endColumn="36"
            endOffset="2869"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `10sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo_two.xml"
            line="22"
            column="13"
            startOffset="780"
            endLine="22"
            endColumn="36"
            endOffset="803"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `10sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo_two.xml"
            line="40"
            column="13"
            startOffset="1473"
            endLine="40"
            endColumn="36"
            endOffset="1496"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `10sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo_two.xml"
            line="53"
            column="13"
            startOffset="1942"
            endLine="53"
            endColumn="36"
            endOffset="1965"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `10sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo_two.xml"
            line="65"
            column="13"
            startOffset="2377"
            endLine="65"
            endColumn="36"
            endOffset="2400"/>
    </incident>

    <incident
        id="SmallSp"
        severity="warning"
        message="Avoid using sizes smaller than `11sp`: `10sp`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo_two.xml"
            line="78"
            column="13"
            startOffset="2846"
            endLine="78"
            endColumn="36"
            endOffset="2869"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download_list.xml"
            line="37"
            column="6"
            startOffset="1270"
            endLine="37"
            endColumn="14"
            endOffset="1278"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/banklay.xml"
            line="9"
            column="6"
            startOffset="249"
            endLine="9"
            endColumn="14"
            endOffset="257"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/banklay.xml"
            line="32"
            column="14"
            startOffset="1012"
            endLine="32"
            endColumn="22"
            endOffset="1020"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/banklay.xml"
            line="46"
            column="14"
            startOffset="1581"
            endLine="46"
            endColumn="22"
            endOffset="1589"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/banklay.xml"
            line="65"
            column="14"
            startOffset="2312"
            endLine="65"
            endColumn="22"
            endOffset="2320"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/banklay.xml"
            line="79"
            column="14"
            startOffset="2883"
            endLine="79"
            endColumn="22"
            endOffset="2891"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/banklay.xml"
            line="98"
            column="14"
            startOffset="3574"
            endLine="98"
            endColumn="22"
            endOffset="3582"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/banklay.xml"
            line="112"
            column="14"
            startOffset="4163"
            endLine="112"
            endColumn="22"
            endOffset="4171"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="25"
            column="14"
            startOffset="888"
            endLine="25"
            endColumn="22"
            endOffset="896"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="42"
            column="14"
            startOffset="1501"
            endLine="42"
            endColumn="22"
            endOffset="1509"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="61"
            column="14"
            startOffset="2147"
            endLine="61"
            endColumn="22"
            endOffset="2155"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="79"
            column="14"
            startOffset="2814"
            endLine="79"
            endColumn="22"
            endOffset="2822"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="90"
            column="14"
            startOffset="3286"
            endLine="90"
            endColumn="22"
            endOffset="3294"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="101"
            column="14"
            startOffset="3751"
            endLine="101"
            endColumn="22"
            endOffset="3759"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="131"
            column="14"
            startOffset="4758"
            endLine="131"
            endColumn="22"
            endOffset="4766"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="149"
            column="14"
            startOffset="5416"
            endLine="149"
            endColumn="22"
            endOffset="5424"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="169"
            column="14"
            startOffset="6101"
            endLine="169"
            endColumn="22"
            endOffset="6109"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="188"
            column="14"
            startOffset="6741"
            endLine="188"
            endColumn="22"
            endOffset="6749"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="226"
            column="18"
            startOffset="8148"
            endLine="226"
            endColumn="26"
            endOffset="8156"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="25"
            column="14"
            startOffset="888"
            endLine="25"
            endColumn="22"
            endOffset="896"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="42"
            column="14"
            startOffset="1501"
            endLine="42"
            endColumn="22"
            endOffset="1509"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="61"
            column="14"
            startOffset="2147"
            endLine="61"
            endColumn="22"
            endOffset="2155"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="79"
            column="14"
            startOffset="2814"
            endLine="79"
            endColumn="22"
            endOffset="2822"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="90"
            column="14"
            startOffset="3286"
            endLine="90"
            endColumn="22"
            endOffset="3294"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="101"
            column="14"
            startOffset="3751"
            endLine="101"
            endColumn="22"
            endOffset="3759"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="131"
            column="14"
            startOffset="4758"
            endLine="131"
            endColumn="22"
            endOffset="4766"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="149"
            column="14"
            startOffset="5416"
            endLine="149"
            endColumn="22"
            endOffset="5424"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="169"
            column="14"
            startOffset="6104"
            endLine="169"
            endColumn="22"
            endOffset="6112"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="188"
            column="14"
            startOffset="6744"
            endLine="188"
            endColumn="22"
            endOffset="6752"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="226"
            column="18"
            startOffset="8151"
            endLine="226"
            endColumn="26"
            endOffset="8159"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_add_expense.xml"
            line="8"
            column="6"
            startOffset="269"
            endLine="8"
            endColumn="14"
            endOffset="277"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_add_expense.xml"
            line="15"
            column="6"
            startOffset="490"
            endLine="15"
            endColumn="14"
            endOffset="498"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_add_expense.xml"
            line="21"
            column="6"
            startOffset="672"
            endLine="21"
            endColumn="14"
            endOffset="680"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_add_expense.xml"
            line="29"
            column="6"
            startOffset="915"
            endLine="29"
            endColumn="14"
            endOffset="923"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_add_income.xml"
            line="9"
            column="6"
            startOffset="379"
            endLine="9"
            endColumn="14"
            endOffset="387"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_add_income.xml"
            line="19"
            column="6"
            startOffset="741"
            endLine="19"
            endColumn="14"
            endOffset="749"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/footer_main.xml"
            line="8"
            column="6"
            startOffset="277"
            endLine="8"
            endColumn="14"
            endOffset="285"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_update_delete.xml"
            line="19"
            column="6"
            startOffset="722"
            endLine="19"
            endColumn="14"
            endOffset="730"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/header_main.xml"
            line="13"
            column="10"
            startOffset="426"
            endLine="13"
            endColumn="18"
            endOffset="434"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/header_main.xml"
            line="23"
            column="10"
            startOffset="821"
            endLine="23"
            endColumn="18"
            endOffset="829"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/header_main.xml"
            line="41"
            column="10"
            startOffset="1449"
            endLine="41"
            endColumn="18"
            endOffset="1457"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/header_main.xml"
            line="51"
            column="10"
            startOffset="1860"
            endLine="51"
            endColumn="18"
            endOffset="1868"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/header_main.xml"
            line="64"
            column="6"
            startOffset="2371"
            endLine="64"
            endColumn="14"
            endOffset="2379"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/header_main.xml"
            line="74"
            column="6"
            startOffset="2728"
            endLine="74"
            endColumn="14"
            endOffset="2736"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item.xml"
            line="11"
            column="10"
            startOffset="349"
            endLine="11"
            endColumn="18"
            endOffset="357"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item.xml"
            line="24"
            column="10"
            startOffset="829"
            endLine="24"
            endColumn="18"
            endOffset="837"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item.xml"
            line="36"
            column="10"
            startOffset="1269"
            endLine="36"
            endColumn="18"
            endOffset="1277"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item.xml"
            line="48"
            column="10"
            startOffset="1715"
            endLine="48"
            endColumn="18"
            endOffset="1723"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item.xml"
            line="60"
            column="10"
            startOffset="2161"
            endLine="60"
            endColumn="18"
            endOffset="2169"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_two.xml"
            line="11"
            column="10"
            startOffset="349"
            endLine="11"
            endColumn="18"
            endOffset="357"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_two.xml"
            line="24"
            column="10"
            startOffset="829"
            endLine="24"
            endColumn="18"
            endOffset="837"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_two.xml"
            line="36"
            column="10"
            startOffset="1269"
            endLine="36"
            endColumn="18"
            endOffset="1277"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_two.xml"
            line="48"
            column="10"
            startOffset="1715"
            endLine="48"
            endColumn="18"
            endOffset="1723"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_two.xml"
            line="60"
            column="10"
            startOffset="2161"
            endLine="60"
            endColumn="18"
            endOffset="2169"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table.xml"
            line="11"
            column="10"
            startOffset="349"
            endLine="11"
            endColumn="18"
            endOffset="357"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table.xml"
            line="26"
            column="10"
            startOffset="876"
            endLine="26"
            endColumn="18"
            endOffset="884"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table.xml"
            line="43"
            column="10"
            startOffset="1483"
            endLine="43"
            endColumn="18"
            endOffset="1491"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table.xml"
            line="57"
            column="10"
            startOffset="1972"
            endLine="57"
            endColumn="18"
            endOffset="1980"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table.xml"
            line="70"
            column="10"
            startOffset="2427"
            endLine="70"
            endColumn="18"
            endOffset="2435"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table_two.xml"
            line="11"
            column="10"
            startOffset="349"
            endLine="11"
            endColumn="18"
            endOffset="357"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table_two.xml"
            line="26"
            column="10"
            startOffset="876"
            endLine="26"
            endColumn="18"
            endOffset="884"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table_two.xml"
            line="43"
            column="10"
            startOffset="1483"
            endLine="43"
            endColumn="18"
            endOffset="1491"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table_two.xml"
            line="57"
            column="10"
            startOffset="1972"
            endLine="57"
            endColumn="18"
            endOffset="1980"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table_two.xml"
            line="70"
            column="10"
            startOffset="2427"
            endLine="70"
            endColumn="18"
            endOffset="2435"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo.xml"
            line="11"
            column="10"
            startOffset="349"
            endLine="11"
            endColumn="18"
            endOffset="357"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo.xml"
            line="25"
            column="10"
            startOffset="857"
            endLine="25"
            endColumn="18"
            endOffset="865"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo.xml"
            line="42"
            column="10"
            startOffset="1511"
            endLine="42"
            endColumn="18"
            endOffset="1519"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo.xml"
            line="55"
            column="10"
            startOffset="1980"
            endLine="55"
            endColumn="18"
            endOffset="1988"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo.xml"
            line="67"
            column="10"
            startOffset="2415"
            endLine="67"
            endColumn="18"
            endOffset="2423"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo_two.xml"
            line="11"
            column="10"
            startOffset="349"
            endLine="11"
            endColumn="18"
            endOffset="357"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo_two.xml"
            line="25"
            column="10"
            startOffset="857"
            endLine="25"
            endColumn="18"
            endOffset="865"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo_two.xml"
            line="42"
            column="10"
            startOffset="1511"
            endLine="42"
            endColumn="18"
            endOffset="1519"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo_two.xml"
            line="55"
            column="10"
            startOffset="1980"
            endLine="55"
            endColumn="18"
            endOffset="1988"/>
    </incident>

    <incident
        id="Autofill"
        severity="warning"
        message="Missing `autofillHints` attribute">
        <fix-alternatives>
            <fix-attribute
                description="Set autofillHints"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="autofillHints"
                value=""
                point="0"
                mark="0"/>
            <fix-attribute
                description="Set importantForAutofill=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAutofill"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo_two.xml"
            line="67"
            column="10"
            startOffset="2415"
            endLine="67"
            endColumn="18"
            endOffset="2423"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for itextpdf"
            robot="true">
            <fix-replace
                description="Replace with itextpdf = &quot;5.5.12&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="itextpdf = &quot;5.5.12&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with itextpdf = { module = &quot;com.itextpdf:itextpdf&quot;, version.ref = &quot;itextpdf&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="itextpdf = { module = &quot;com.itextpdf:itextpdf&quot;, version.ref = &quot;itextpdf&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="228"
                    endOffset="228"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.itextpdf"
                robot="true"
                replacement="libs.itextpdf"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="1362"
                    endOffset="1392"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="51"
            column="20"
            startOffset="1362"
            endLine="51"
            endColumn="50"
            endOffset="1392"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for dexter"
            robot="true">
            <fix-replace
                description="Replace with dexter = &quot;6.2.3&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="dexter = &quot;6.2.3&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with dexter = { module = &quot;com.karumi:dexter&quot;, version.ref = &quot;dexter&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="dexter = { module = &quot;com.karumi:dexter&quot;, version.ref = &quot;dexter&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="228"
                    endOffset="228"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.dexter"
                robot="true"
                replacement="libs.dexter"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="1463"
                    endOffset="1488"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="53"
            column="20"
            startOffset="1463"
            endLine="53"
            endColumn="45"
            endOffset="1488"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for firebase-bom"
            robot="true">
            <fix-replace
                description="Replace with firebaseBom = &quot;33.4.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="firebaseBom = &quot;33.4.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with firebase-bom = { module = &quot;com.google.firebase:firebase-bom&quot;, version.ref = &quot;firebaseBom&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="firebase-bom = { module = &quot;com.google.firebase:firebase-bom&quot;, version.ref = &quot;firebaseBom&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="228"
                    endOffset="228"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.firebase.bom"
                robot="true"
                replacement="libs.firebase.bom"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="1556"
                    endOffset="1607"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="55"
            column="20"
            startOffset="1556"
            endLine="55"
            endColumn="71"
            endOffset="1607"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for firebase-database"
            robot="true">
            <fix-replace
                description="Replace with firebaseDatabase = &quot;21.0.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="firebaseDatabase = &quot;21.0.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with firebase-database = { module = &quot;com.google.firebase:firebase-database&quot;, version.ref = &quot;firebaseDatabase&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="firebase-database = { module = &quot;com.google.firebase:firebase-database&quot;, version.ref = &quot;firebaseDatabase&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="228"
                    endOffset="228"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.firebase.database"
                robot="true"
                replacement="libs.firebase.database"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="1628"
                    endOffset="1674"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="56"
            column="20"
            startOffset="1628"
            endLine="56"
            endColumn="66"
            endOffset="1674"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for firebase-analytics-ktx"
            robot="true">
            <fix-replace
                description="Replace with firebaseAnalyticsKtx = &quot;22.1.2&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="firebaseAnalyticsKtx = &quot;22.1.2&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with firebase-analytics-ktx = { module = &quot;com.google.firebase:firebase-analytics-ktx&quot;, version.ref = &quot;firebaseAnalyticsKtx&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="firebase-analytics-ktx = { module = &quot;com.google.firebase:firebase-analytics-ktx&quot;, version.ref = &quot;firebaseAnalyticsKtx&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="228"
                    endOffset="228"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.firebase.analytics.ktx"
                robot="true"
                replacement="libs.firebase.analytics.ktx"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="1724"
                    endOffset="1775"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="57"
            column="20"
            startOffset="1724"
            endLine="57"
            endColumn="71"
            endOffset="1775"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for firebase-storage"
            robot="true">
            <fix-replace
                description="Replace with firebase-storage = { module = &quot;com.google.firebase:firebase-storage&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="firebase-storage = { module = &quot;com.google.firebase:firebase-storage&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="228"
                    endOffset="228"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.firebase.storage"
                robot="true"
                replacement="libs.firebase.storage"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="1972"
                    endOffset="2010"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="61"
            column="20"
            startOffset="1972"
            endLine="61"
            endColumn="58"
            endOffset="2010"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for biometric"
            robot="true">
            <fix-replace
                description="Replace with biometric = &quot;1.1.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="biometric = &quot;1.1.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with biometric = { module = &quot;androidx.biometric:biometric&quot;, version.ref = &quot;biometric&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="biometric = { module = &quot;androidx.biometric:biometric&quot;, version.ref = &quot;biometric&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="228"
                    endOffset="228"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.biometric"
                robot="true"
                replacement="libs.biometric"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="2031"
                    endOffset="2067"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="62"
            column="20"
            startOffset="2031"
            endLine="62"
            endColumn="56"
            endOffset="2067"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for gson"
            robot="true">
            <fix-replace
                description="Replace with gson = &quot;2.10.1&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="gson = &quot;2.10.1&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with gson = { module = &quot;com.google.code.gson:gson&quot;, version.ref = &quot;gson&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="gson = { module = &quot;com.google.code.gson:gson&quot;, version.ref = &quot;gson&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="228"
                    endOffset="228"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.gson"
                robot="true"
                replacement="libs.gson"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="2087"
                    endOffset="2121"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="63"
            column="20"
            startOffset="2087"
            endLine="63"
            endColumn="54"
            endOffset="2121"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for switch-button"
            robot="true">
            <fix-replace
                description="Replace with switchButton = &quot;0.0.3&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="switchButton = &quot;0.0.3&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="214"
                    endOffset="214"/>
            </fix-replace>
            <fix-replace
                description="Replace with switch-button = { module = &quot;com.github.zcweng:switch-button&quot;, version.ref = &quot;switchButton&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="switch-button = { module = &quot;com.github.zcweng:switch-button&quot;, version.ref = &quot;switchButton&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="1066"
                    endOffset="1066"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.switch.button"
                robot="true"
                replacement="libs.switch.button"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="2141"
                    endOffset="2184"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="64"
            column="20"
            startOffset="2141"
            endLine="64"
            endColumn="63"
            endOffset="2184"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for mpandroidchart"
            robot="true">
            <fix-replace
                description="Replace with mpandroidchart = &quot;v3.1.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="mpandroidchart = &quot;v3.1.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="175"
                    endOffset="175"/>
            </fix-replace>
            <fix-replace
                description="Replace with mpandroidchart = { module = &quot;com.github.PhilJay:MPAndroidChart&quot;, version.ref = &quot;mpandroidchart&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="mpandroidchart = { module = &quot;com.github.PhilJay:MPAndroidChart&quot;, version.ref = &quot;mpandroidchart&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="890"
                    endOffset="890"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.mpandroidchart"
                robot="true"
                replacement="libs.mpandroidchart"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="2218"
                    endOffset="2260"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="66"
            column="20"
            startOffset="2218"
            endLine="66"
            endColumn="62"
            endOffset="2260"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for lottie"
            robot="true">
            <fix-replace
                description="Replace with lottie = &quot;6.4.1&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="lottie = &quot;6.4.1&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="109"
                    endOffset="109"/>
            </fix-replace>
            <fix-replace
                description="Replace with lottie = { module = &quot;com.airbnb.android:lottie&quot;, version.ref = &quot;lottie&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="lottie = { module = &quot;com.airbnb.android:lottie&quot;, version.ref = &quot;lottie&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="584"
                    endOffset="584"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.lottie"
                robot="true"
                replacement="libs.lottie"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="2280"
                    endOffset="2313"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="67"
            column="20"
            startOffset="2280"
            endLine="67"
            endColumn="53"
            endOffset="2313"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for glide"
            robot="true">
            <fix-replace
                description="Replace with glide = &quot;4.12.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="glide = &quot;4.12.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with glide = { module = &quot;com.github.bumptech.glide:glide&quot;, version.ref = &quot;glide&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="glide = { module = &quot;com.github.bumptech.glide:glide&quot;, version.ref = &quot;glide&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="228"
                    endOffset="228"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.glide"
                robot="true"
                replacement="libs.glide"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="2368"
                    endOffset="2408"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="69"
            column="20"
            startOffset="2368"
            endLine="69"
            endColumn="60"
            endOffset="2408"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for compiler"
            robot="true">
            <fix-replace
                description="Replace with compiler = &quot;4.12.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="compiler = &quot;4.12.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="26"
                    endOffset="26"/>
            </fix-replace>
            <fix-replace
                description="Replace with compiler = { module = &quot;com.github.bumptech.glide:compiler&quot;, version.ref = &quot;compiler&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="compiler = { module = &quot;com.github.bumptech.glide:compiler&quot;, version.ref = &quot;compiler&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="228"
                    endOffset="228"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.compiler"
                robot="true"
                replacement="libs.compiler"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="2433"
                    endOffset="2476"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="70"
            column="25"
            startOffset="2433"
            endLine="70"
            endColumn="68"
            endOffset="2476"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for okhttp"
            robot="true">
            <fix-replace
                description="Replace with okhttp = &quot;4.12.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="okhttp = &quot;4.12.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="175"
                    endOffset="175"/>
            </fix-replace>
            <fix-replace
                description="Replace with okhttp = { module = &quot;com.squareup.okhttp3:okhttp&quot;, version.ref = &quot;okhttp&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="okhttp = { module = &quot;com.squareup.okhttp3:okhttp&quot;, version.ref = &quot;okhttp&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="890"
                    endOffset="890"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.okhttp"
                robot="true"
                replacement="libs.okhttp"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="2558"
                    endOffset="2594"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="73"
            column="20"
            startOffset="2558"
            endLine="73"
            endColumn="56"
            endOffset="2594"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for logging-interceptor"
            robot="true">
            <fix-replace
                description="Replace with loggingInterceptor = &quot;4.12.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="loggingInterceptor = &quot;4.12.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="109"
                    endOffset="109"/>
            </fix-replace>
            <fix-replace
                description="Replace with logging-interceptor = { module = &quot;com.squareup.okhttp3:logging-interceptor&quot;, version.ref = &quot;loggingInterceptor&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="logging-interceptor = { module = &quot;com.squareup.okhttp3:logging-interceptor&quot;, version.ref = &quot;loggingInterceptor&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="584"
                    endOffset="584"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.logging.interceptor"
                robot="true"
                replacement="libs.logging.interceptor"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="2614"
                    endOffset="2663"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="74"
            column="20"
            startOffset="2614"
            endLine="74"
            endColumn="69"
            endOffset="2663"/>
    </incident>

    <incident
        id="UseTomlInstead"
        severity="warning"
        message="Use version catalog instead">
        <fix-composite
            description="Replace with new library catalog declaration for okhttp-urlconnection"
            robot="true">
            <fix-replace
                description="Replace with okhttpUrlconnection = &quot;4.12.0&quot;..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="okhttpUrlconnection = &quot;4.12.0&quot;&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="175"
                    endOffset="175"/>
            </fix-replace>
            <fix-replace
                description="Replace with okhttp-urlconnection = { module = &quot;com.squareup.okhttp3:okhttp-urlconnection&quot;, version.ref = &quot;okhttpUrlconnection&quot; }..."
                robot="true"
                oldString="_lint_insert_begin_"
                replacement="okhttp-urlconnection = { module = &quot;com.squareup.okhttp3:okhttp-urlconnection&quot;, version.ref = &quot;okhttpUrlconnection&quot; }&#xA;"
                priority="0">
                <range
                    file="../gradle/libs.versions.toml"
                    startOffset="890"
                    endOffset="890"/>
            </fix-replace>
            <fix-replace
                description="Replace with libs.okhttp.urlconnection"
                robot="true"
                replacement="libs.okhttp.urlconnection"
                priority="0">
                <range
                    file="${:app*projectDir}/build.gradle"
                    startOffset="2683"
                    endOffset="2733"/>
            </fix-replace>
        </fix-composite>
        <location
            file="${:app*projectDir}/build.gradle"
            line="75"
            column="20"
            startOffset="2683"
            endLine="75"
            endColumn="70"
            endOffset="2733"/>
    </incident>

    <incident
        id="ClickableViewAccessibility"
        severity="warning"
        message="`onTouch` should call `View#performClick` when a click is detected">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/CustomButtonEffect.java"
            line="44"
            column="28"
            startOffset="1600"
            endLine="44"
            endColumn="35"
            endOffset="1607"/>
    </incident>

    <incident
        id="ClickableViewAccessibility"
        severity="warning"
        message="Custom view ``LinearLayout`` has `setOnTouchListener` called on it but does not override `performClick`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/Home.java"
            line="207"
            column="9"
            startOffset="8559"
            endLine="235"
            endColumn="11"
            endOffset="9800"/>
    </incident>

    <incident
        id="ClickableViewAccessibility"
        severity="warning"
        message="`onTouch` should call `View#performClick` when a click is detected">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/Home.java"
            line="209"
            column="28"
            startOffset="8665"
            endLine="209"
            endColumn="35"
            endOffset="8672"/>
    </incident>

    <incident
        id="ClickableViewAccessibility"
        severity="warning"
        message="Custom view ``LinearLayout`` has `setOnTouchListener` called on it but does not override `performClick`">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/InvoiceTwo.java"
            line="196"
            column="9"
            startOffset="8070"
            endLine="224"
            endColumn="11"
            endOffset="9311"/>
    </incident>

    <incident
        id="ClickableViewAccessibility"
        severity="warning"
        message="`onTouch` should call `View#performClick` when a click is detected">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/InvoiceTwo.java"
            line="198"
            column="28"
            startOffset="8176"
            endLine="198"
            endColumn="35"
            endOffset="8183"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_home.xml"
            line="116"
            column="14"
            startOffset="4304"
            endLine="116"
            endColumn="23"
            endOffset="4313"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_home.xml"
            line="124"
            column="14"
            startOffset="4633"
            endLine="124"
            endColumn="23"
            endOffset="4642"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_home.xml"
            line="144"
            column="14"
            startOffset="5264"
            endLine="144"
            endColumn="23"
            endOffset="5273"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_home.xml"
            line="151"
            column="14"
            startOffset="5517"
            endLine="151"
            endColumn="23"
            endOffset="5526"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_home.xml"
            line="158"
            column="14"
            startOffset="5766"
            endLine="158"
            endColumn="23"
            endOffset="5775"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_home.xml"
            line="167"
            column="14"
            startOffset="6144"
            endLine="167"
            endColumn="23"
            endOffset="6153"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_home.xml"
            line="176"
            column="14"
            startOffset="6528"
            endLine="176"
            endColumn="23"
            endOffset="6537"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_home.xml"
            line="185"
            column="14"
            startOffset="6914"
            endLine="185"
            endColumn="23"
            endOffset="6923"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_home.xml"
            line="203"
            column="14"
            startOffset="7596"
            endLine="203"
            endColumn="23"
            endOffset="7605"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_home.xml"
            line="212"
            column="14"
            startOffset="7980"
            endLine="212"
            endColumn="23"
            endOffset="7989"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_home.xml"
            line="229"
            column="14"
            startOffset="8656"
            endLine="229"
            endColumn="23"
            endOffset="8665"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_invoice_two.xml"
            line="102"
            column="14"
            startOffset="3776"
            endLine="102"
            endColumn="23"
            endOffset="3785"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_invoice_two.xml"
            line="110"
            column="14"
            startOffset="4105"
            endLine="110"
            endColumn="23"
            endOffset="4114"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_invoice_two.xml"
            line="130"
            column="14"
            startOffset="4736"
            endLine="130"
            endColumn="23"
            endOffset="4745"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_invoice_two.xml"
            line="137"
            column="14"
            startOffset="4989"
            endLine="137"
            endColumn="23"
            endOffset="4998"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_invoice_two.xml"
            line="144"
            column="14"
            startOffset="5238"
            endLine="144"
            endColumn="23"
            endOffset="5247"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_invoice_two.xml"
            line="153"
            column="14"
            startOffset="5616"
            endLine="153"
            endColumn="23"
            endOffset="5625"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_invoice_two.xml"
            line="162"
            column="14"
            startOffset="6000"
            endLine="162"
            endColumn="23"
            endOffset="6009"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_invoice_two.xml"
            line="171"
            column="14"
            startOffset="6386"
            endLine="171"
            endColumn="23"
            endOffset="6395"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_invoice_two.xml"
            line="189"
            column="14"
            startOffset="7068"
            endLine="189"
            endColumn="23"
            endOffset="7077"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_invoice_two.xml"
            line="198"
            column="14"
            startOffset="7452"
            endLine="198"
            endColumn="23"
            endOffset="7461"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_selection.xml"
            line="53"
            column="22"
            startOffset="2202"
            endLine="53"
            endColumn="31"
            endOffset="2211"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_selection.xml"
            line="74"
            column="22"
            startOffset="3169"
            endLine="74"
            endColumn="31"
            endOffset="3178"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_selection.xml"
            line="102"
            column="26"
            startOffset="4526"
            endLine="102"
            endColumn="35"
            endOffset="4535"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_selection.xml"
            line="122"
            column="26"
            startOffset="5556"
            endLine="122"
            endColumn="35"
            endOffset="5565"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_selection.xml"
            line="145"
            column="22"
            startOffset="6540"
            endLine="145"
            endColumn="31"
            endOffset="6549"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_varify.xml"
            line="9"
            column="6"
            startOffset="390"
            endLine="9"
            endColumn="15"
            endOffset="399"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fgdata_upload.xml"
            line="167"
            column="18"
            startOffset="8063"
            endLine="167"
            endColumn="29"
            endOffset="8074"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/nav_header.xml"
            line="17"
            column="10"
            startOffset="610"
            endLine="17"
            endColumn="19"
            endOffset="619"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/pdf_item.xml"
            line="71"
            column="18"
            startOffset="2851"
            endLine="71"
            endColumn="27"
            endOffset="2860"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/pdf_item.xml"
            line="78"
            column="18"
            startOffset="3130"
            endLine="78"
            endColumn="27"
            endOffset="3139"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/pdf_item.xml"
            line="85"
            column="18"
            startOffset="3413"
            endLine="85"
            endColumn="27"
            endOffset="3422"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/signature_layout.xml"
            line="7"
            column="6"
            startOffset="241"
            endLine="7"
            endColumn="15"
            endOffset="250"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/DownloadListActivity.java"
            line="264"
            column="45"
            startOffset="10643"
            endLine="264"
            endColumn="59"
            endOffset="10657"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/DownloadListActivity.java"
            line="265"
            column="40"
            startOffset="10715"
            endLine="265"
            endColumn="54"
            endOffset="10729"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/Home.java"
            line="135"
            column="22"
            startOffset="5620"
            endLine="135"
            endColumn="42"
            endOffset="5640"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/Home.java"
            line="135"
            column="22"
            startOffset="5620"
            endLine="135"
            endColumn="30"
            endOffset="5628"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/Home.java"
            line="140"
            column="28"
            startOffset="5870"
            endLine="140"
            endColumn="66"
            endOffset="5908"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/Home.java"
            line="140"
            column="28"
            startOffset="5870"
            endLine="140"
            endColumn="41"
            endOffset="5883"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/Home.java"
            line="140"
            column="61"
            startOffset="5903"
            endLine="140"
            endColumn="66"
            endOffset="5908"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/Home.java"
            line="433"
            column="47"
            startOffset="17222"
            endLine="433"
            endColumn="63"
            endOffset="17238"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/Home.java"
            line="566"
            column="27"
            startOffset="22494"
            endLine="566"
            endColumn="74"
            endOffset="22541"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/Home.java"
            line="745"
            column="31"
            startOffset="29697"
            endLine="745"
            endColumn="48"
            endOffset="29714"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/InvoiceTwo.java"
            line="130"
            column="22"
            startOffset="5394"
            endLine="130"
            endColumn="42"
            endOffset="5414"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/InvoiceTwo.java"
            line="130"
            column="22"
            startOffset="5394"
            endLine="130"
            endColumn="30"
            endOffset="5402"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/InvoiceTwo.java"
            line="334"
            column="26"
            startOffset="13237"
            endLine="334"
            endColumn="74"
            endOffset="13285"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/InvoiceTwo.java"
            line="385"
            column="47"
            startOffset="15234"
            endLine="385"
            endColumn="131"
            endOffset="15318"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/InvoiceTwo.java"
            line="385"
            column="47"
            startOffset="15234"
            endLine="385"
            endColumn="67"
            endOffset="15254"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/InvoiceTwo.java"
            line="385"
            column="84"
            startOffset="15271"
            endLine="385"
            endColumn="99"
            endOffset="15286"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/InvoiceTwo.java"
            line="385"
            column="118"
            startOffset="15305"
            endLine="385"
            endColumn="131"
            endOffset="15318"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/InvoiceTwo.java"
            line="388"
            column="47"
            startOffset="15502"
            endLine="388"
            endColumn="63"
            endOffset="15518"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/InvoiceTwo.java"
            line="613"
            column="31"
            startOffset="24302"
            endLine="613"
            endColumn="48"
            endOffset="24319"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/UpdateDeleteFragment.java"
            line="323"
            column="44"
            startOffset="14397"
            endLine="323"
            endColumn="83"
            endOffset="14436"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/UpdateDeleteFragment.java"
            line="323"
            column="44"
            startOffset="14397"
            endLine="323"
            endColumn="59"
            endOffset="14412"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/UpdateDeleteFragment.java"
            line="324"
            column="41"
            startOffset="14479"
            endLine="324"
            endColumn="74"
            endOffset="14512"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/UpdateDeleteFragment.java"
            line="324"
            column="41"
            startOffset="14479"
            endLine="324"
            endColumn="53"
            endOffset="14491"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/UpdateDeleteFragment.java"
            line="325"
            column="35"
            startOffset="14549"
            endLine="325"
            endColumn="61"
            endOffset="14575"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/UpdateDeleteFragment.java"
            line="325"
            column="35"
            startOffset="14549"
            endLine="325"
            endColumn="46"
            endOffset="14560"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/UpdateDeleteFragment.java"
            line="326"
            column="36"
            startOffset="14613"
            endLine="326"
            endColumn="59"
            endOffset="14636"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/UpdateDeleteFragment.java"
            line="326"
            column="36"
            startOffset="14613"
            endLine="326"
            endColumn="43"
            endOffset="14620"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/UpdateDeleteFragment.java"
            line="327"
            column="36"
            startOffset="14674"
            endLine="327"
            endColumn="70"
            endOffset="14708"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/UpdateDeleteFragment.java"
            line="327"
            column="36"
            startOffset="14674"
            endLine="327"
            endColumn="54"
            endOffset="14692"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/UpdateDeleteFragment.java"
            line="328"
            column="39"
            startOffset="14749"
            endLine="328"
            endColumn="68"
            endOffset="14778"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/UpdateDeleteFragment.java"
            line="328"
            column="39"
            startOffset="14749"
            endLine="328"
            endColumn="49"
            endOffset="14759"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/UpdateDeleteFragment.java"
            line="329"
            column="36"
            startOffset="14816"
            endLine="329"
            endColumn="59"
            endOffset="14839"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/UpdateDeleteFragment.java"
            line="330"
            column="46"
            startOffset="14887"
            endLine="330"
            endColumn="90"
            endOffset="14931"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/UpdateDeleteFragment.java"
            line="330"
            column="46"
            startOffset="14887"
            endLine="330"
            endColumn="64"
            endOffset="14905"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/UpdateDeleteFragment.java"
            line="332"
            column="37"
            startOffset="14971"
            endLine="332"
            endColumn="83"
            endOffset="15017"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/VarifyActivity.java"
            line="51"
            column="38"
            startOffset="1915"
            endLine="51"
            endColumn="74"
            endOffset="1951"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/VarifyActivity.java"
            line="51"
            column="38"
            startOffset="1915"
            endLine="51"
            endColumn="62"
            endOffset="1939"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/VarifyActivity.java"
            line="67"
            column="38"
            startOffset="2673"
            endLine="67"
            endColumn="62"
            endOffset="2697"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/VarifyActivity.java"
            line="80"
            column="30"
            startOffset="3341"
            endLine="80"
            endColumn="89"
            endOffset="3400"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/official/invoicegenarator/VarifyActivity.java"
            line="82"
            column="30"
            startOffset="3559"
            endLine="82"
            endColumn="82"
            endOffset="3611"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;নতৃুন আপডেট&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download_list.xml"
            line="25"
            column="17"
            startOffset="931"
            endLine="25"
            endColumn="43"
            endOffset="957"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;পুরাতন আপডেট&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download_list.xml"
            line="32"
            column="17"
            startOffset="1191"
            endLine="32"
            endColumn="44"
            endOffset="1218"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Search PDFs&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_download_list.xml"
            line="44"
            column="9"
            startOffset="1564"
            endLine="44"
            endColumn="35"
            endOffset="1590"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Click Icon for Biometric Authentication&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_varify.xml"
            line="28"
            column="9"
            startOffset="1127"
            endLine="28"
            endColumn="63"
            endOffset="1181"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Banking Account Details&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/banklay.xml"
            line="20"
            column="9"
            startOffset="671"
            endLine="20"
            endColumn="47"
            endOffset="709"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Bank Name&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/banklay.xml"
            line="41"
            column="17"
            startOffset="1436"
            endLine="41"
            endColumn="41"
            endOffset="1460"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Bank Muscat&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/banklay.xml"
            line="55"
            column="17"
            startOffset="2005"
            endLine="55"
            endColumn="43"
            endOffset="2031"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Account No&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/banklay.xml"
            line="72"
            column="17"
            startOffset="2634"
            endLine="72"
            endColumn="42"
            endOffset="2659"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;****************&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/banklay.xml"
            line="86"
            column="17"
            startOffset="3205"
            endLine="86"
            endColumn="48"
            endOffset="3236"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Account name holder&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/banklay.xml"
            line="104"
            column="17"
            startOffset="3841"
            endLine="104"
            endColumn="51"
            endOffset="3875"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;NABRAS AL KHOUDH TRAD &amp; CON. PROJECT&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/banklay.xml"
            line="119"
            column="17"
            startOffset="4494"
            endLine="119"
            endColumn="72"
            endOffset="4549"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;VAT No: OM110000378X&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="107"
            column="17"
            startOffset="4035"
            endLine="107"
            endColumn="52"
            endOffset="4070"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Date :.........&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="138"
            column="17"
            startOffset="5082"
            endLine="138"
            endColumn="47"
            endOffset="5112"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;QR No : QR 210724SIB&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="156"
            column="17"
            startOffset="5746"
            endLine="156"
            endColumn="52"
            endOffset="5781"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Location : AKO Branch&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="175"
            column="17"
            startOffset="6385"
            endLine="175"
            endColumn="53"
            endOffset="6421"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;VAT IN : OM1100198193&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="194"
            column="17"
            startOffset="7025"
            endLine="194"
            endColumn="53"
            endOffset="7061"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Enter text here&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="231"
            column="21"
            startOffset="8406"
            endLine="231"
            endColumn="51"
            endOffset="8436"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;SUBJECT:   &quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="232"
            column="21"
            startOffset="8458"
            endLine="232"
            endColumn="47"
            endOffset="8484"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;VAT No: OM110000378X&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="107"
            column="17"
            startOffset="4035"
            endLine="107"
            endColumn="52"
            endOffset="4070"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Date :.........&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="138"
            column="17"
            startOffset="5082"
            endLine="138"
            endColumn="47"
            endOffset="5112"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;QR No : QR 210724SIB&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="156"
            column="17"
            startOffset="5749"
            endLine="156"
            endColumn="52"
            endOffset="5784"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Location : AKO Branch&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="175"
            column="17"
            startOffset="6388"
            endLine="175"
            endColumn="53"
            endOffset="6424"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;VAT IN : OM1100198193&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="194"
            column="17"
            startOffset="7028"
            endLine="194"
            endColumn="53"
            endOffset="7064"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Enter text here&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="231"
            column="21"
            startOffset="8409"
            endLine="231"
            endColumn="51"
            endOffset="8439"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;SUBJECT:   &quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main_two.xml"
            line="232"
            column="21"
            startOffset="8461"
            endLine="232"
            endColumn="47"
            endOffset="8487"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Enter file name&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/custom_file_name_dialog.xml"
            line="18"
            column="9"
            startOffset="704"
            endLine="18"
            endColumn="39"
            endOffset="734"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Enter File Name&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/custom_file_name_dialog.xml"
            line="26"
            column="9"
            startOffset="1058"
            endLine="26"
            endColumn="39"
            endOffset="1088"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Cancel&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/custom_file_name_dialog.xml"
            line="55"
            column="13"
            startOffset="2188"
            endLine="55"
            endColumn="34"
            endOffset="2209"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Save&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/custom_file_name_dialog.xml"
            line="65"
            column="13"
            startOffset="2570"
            endLine="65"
            endColumn="32"
            endOffset="2589"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Amount&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_add_expense.xml"
            line="12"
            column="9"
            startOffset="414"
            endLine="12"
            endColumn="30"
            endOffset="435"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Category&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_add_expense.xml"
            line="19"
            column="9"
            startOffset="637"
            endLine="19"
            endColumn="32"
            endOffset="660"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Date&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_add_expense.xml"
            line="25"
            column="9"
            startOffset="815"
            endLine="25"
            endColumn="28"
            endOffset="834"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Note&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_add_expense.xml"
            line="33"
            column="9"
            startOffset="1058"
            endLine="33"
            endColumn="28"
            endOffset="1077"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Pick Date&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_add_expense.xml"
            line="40"
            column="9"
            startOffset="1269"
            endLine="40"
            endColumn="33"
            endOffset="1293"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Amount&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_add_income.xml"
            line="13"
            column="9"
            startOffset="515"
            endLine="13"
            endColumn="30"
            endOffset="536"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Note&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_add_income.xml"
            line="23"
            column="9"
            startOffset="875"
            endLine="23"
            endColumn="28"
            endOffset="894"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Date&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_add_income.xml"
            line="40"
            column="9"
            startOffset="1518"
            endLine="40"
            endColumn="28"
            endOffset="1537"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Save&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_add_income.xml"
            line="48"
            column="9"
            startOffset="1808"
            endLine="48"
            endColumn="28"
            endOffset="1827"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Are you sure you want to close First Invoice?&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_exit_confirmation.xml"
            line="24"
            column="13"
            startOffset="907"
            endLine="24"
            endColumn="73"
            endOffset="967"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Don&apos;t show this again&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_exit_confirmation.xml"
            line="35"
            column="13"
            startOffset="1354"
            endLine="35"
            endColumn="49"
            endOffset="1390"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Cancel&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_exit_confirmation.xml"
            line="52"
            column="17"
            startOffset="1996"
            endLine="52"
            endColumn="38"
            endOffset="2017"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Yes&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_exit_confirmation.xml"
            line="59"
            column="17"
            startOffset="2255"
            endLine="59"
            endColumn="35"
            endOffset="2273"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Number to Text Converter:&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_number_to_words.xml"
            line="12"
            column="9"
            startOffset="435"
            endLine="12"
            endColumn="49"
            endOffset="475"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Enter number&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_number_to_words.xml"
            line="31"
            column="13"
            startOffset="1213"
            endLine="31"
            endColumn="40"
            endOffset="1240"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Copy&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_number_to_words.xml"
            line="53"
            column="9"
            startOffset="1966"
            endLine="53"
            endColumn="28"
            endOffset="1985"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Downloading PDF...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_progress.xml"
            line="20"
            column="9"
            startOffset="715"
            endLine="20"
            endColumn="42"
            endOffset="748"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Enter file name (without .pdf)&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_save_pdf.xml"
            line="12"
            column="9"
            startOffset="480"
            endLine="12"
            endColumn="54"
            endOffset="525"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Description&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_update_item.xml"
            line="17"
            column="13"
            startOffset="675"
            endLine="17"
            endColumn="39"
            endOffset="701"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Location&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_update_item.xml"
            line="31"
            column="13"
            startOffset="1326"
            endLine="31"
            endColumn="36"
            endOffset="1349"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;QR Code&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_update_item.xml"
            line="45"
            column="13"
            startOffset="1971"
            endLine="45"
            endColumn="35"
            endOffset="1993"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;LPO&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_update_item.xml"
            line="59"
            column="13"
            startOffset="2609"
            endLine="59"
            endColumn="31"
            endOffset="2627"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Invoice Number&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_update_item.xml"
            line="73"
            column="13"
            startOffset="3244"
            endLine="73"
            endColumn="42"
            endOffset="3273"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Amount&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_update_item.xml"
            line="87"
            column="13"
            startOffset="3890"
            endLine="87"
            endColumn="34"
            endOffset="3911"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;W/A&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_update_item.xml"
            line="101"
            column="13"
            startOffset="4531"
            endLine="101"
            endColumn="31"
            endOffset="4549"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Payment Status&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/dialog_update_item.xml"
            line="115"
            column="13"
            startOffset="5166"
            endLine="115"
            endColumn="42"
            endOffset="5195"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Invoice Tracker&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fgdata_upload.xml"
            line="31"
            column="13"
            startOffset="1156"
            endLine="31"
            endColumn="43"
            endOffset="1186"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Description&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fgdata_upload.xml"
            line="50"
            column="21"
            startOffset="1919"
            endLine="50"
            endColumn="47"
            endOffset="1945"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Description&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fgdata_upload.xml"
            line="59"
            column="25"
            startOffset="2412"
            endLine="59"
            endColumn="51"
            endOffset="2438"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Location&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fgdata_upload.xml"
            line="65"
            column="21"
            startOffset="2726"
            endLine="65"
            endColumn="44"
            endOffset="2749"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Location&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fgdata_upload.xml"
            line="74"
            column="25"
            startOffset="3213"
            endLine="74"
            endColumn="48"
            endOffset="3236"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;QR&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fgdata_upload.xml"
            line="80"
            column="21"
            startOffset="3524"
            endLine="80"
            endColumn="38"
            endOffset="3541"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;QR&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fgdata_upload.xml"
            line="89"
            column="25"
            startOffset="3999"
            endLine="89"
            endColumn="42"
            endOffset="4016"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;LPO&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fgdata_upload.xml"
            line="95"
            column="21"
            startOffset="4304"
            endLine="95"
            endColumn="39"
            endOffset="4322"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;LPO&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fgdata_upload.xml"
            line="104"
            column="25"
            startOffset="4781"
            endLine="104"
            endColumn="43"
            endOffset="4799"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;INV&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fgdata_upload.xml"
            line="110"
            column="21"
            startOffset="5087"
            endLine="110"
            endColumn="39"
            endOffset="5105"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;INV&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fgdata_upload.xml"
            line="119"
            column="25"
            startOffset="5564"
            endLine="119"
            endColumn="43"
            endOffset="5582"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Amount&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fgdata_upload.xml"
            line="125"
            column="21"
            startOffset="5870"
            endLine="125"
            endColumn="42"
            endOffset="5891"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Amount&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fgdata_upload.xml"
            line="134"
            column="25"
            startOffset="6353"
            endLine="134"
            endColumn="46"
            endOffset="6374"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;W/A&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fgdata_upload.xml"
            line="140"
            column="21"
            startOffset="6662"
            endLine="140"
            endColumn="39"
            endOffset="6680"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;W/A&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fgdata_upload.xml"
            line="149"
            column="25"
            startOffset="7139"
            endLine="149"
            endColumn="43"
            endOffset="7157"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Payment Status&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fgdata_upload.xml"
            line="155"
            column="21"
            startOffset="7445"
            endLine="155"
            endColumn="50"
            endOffset="7474"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Payment Status&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fgdata_upload.xml"
            line="164"
            column="25"
            startOffset="7936"
            endLine="164"
            endColumn="54"
            endOffset="7965"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Upload Data&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fgdata_upload.xml"
            line="174"
            column="21"
            startOffset="8405"
            endLine="174"
            endColumn="47"
            endOffset="8431"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Total amount RO 300 /- (Rial Omani Three Hundred rial-Only)&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/footer_main.xml"
            line="15"
            column="9"
            startOffset="557"
            endLine="15"
            endColumn="83"
            endOffset="631"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Invoice Traker&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_data_upload.xml"
            line="14"
            column="9"
            startOffset="530"
            endLine="14"
            endColumn="38"
            endOffset="559"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Description&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_data_upload.xml"
            line="35"
            column="17"
            startOffset="1285"
            endLine="35"
            endColumn="43"
            endOffset="1311"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Description&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_data_upload.xml"
            line="44"
            column="21"
            startOffset="1746"
            endLine="44"
            endColumn="47"
            endOffset="1772"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Location&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_data_upload.xml"
            line="50"
            column="17"
            startOffset="2040"
            endLine="50"
            endColumn="40"
            endOffset="2063"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Location&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_data_upload.xml"
            line="59"
            column="21"
            startOffset="2495"
            endLine="59"
            endColumn="44"
            endOffset="2518"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;QR&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_data_upload.xml"
            line="65"
            column="17"
            startOffset="2786"
            endLine="65"
            endColumn="34"
            endOffset="2803"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;QR&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_data_upload.xml"
            line="74"
            column="21"
            startOffset="3229"
            endLine="74"
            endColumn="38"
            endOffset="3246"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;LPO&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_data_upload.xml"
            line="80"
            column="17"
            startOffset="3514"
            endLine="80"
            endColumn="35"
            endOffset="3532"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;LPO&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_data_upload.xml"
            line="89"
            column="21"
            startOffset="3959"
            endLine="89"
            endColumn="39"
            endOffset="3977"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;INV&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_data_upload.xml"
            line="95"
            column="17"
            startOffset="4245"
            endLine="95"
            endColumn="35"
            endOffset="4263"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;INV&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_data_upload.xml"
            line="104"
            column="21"
            startOffset="4690"
            endLine="104"
            endColumn="39"
            endOffset="4708"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Amount&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_data_upload.xml"
            line="110"
            column="17"
            startOffset="4976"
            endLine="110"
            endColumn="38"
            endOffset="4997"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Amount&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_data_upload.xml"
            line="119"
            column="21"
            startOffset="5427"
            endLine="119"
            endColumn="42"
            endOffset="5448"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;W/A&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_data_upload.xml"
            line="125"
            column="17"
            startOffset="5716"
            endLine="125"
            endColumn="35"
            endOffset="5734"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;W/A&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_data_upload.xml"
            line="134"
            column="21"
            startOffset="6161"
            endLine="134"
            endColumn="39"
            endOffset="6179"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Payment Status&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_data_upload.xml"
            line="139"
            column="17"
            startOffset="6445"
            endLine="139"
            endColumn="46"
            endOffset="6474"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Payment Status&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_data_upload.xml"
            line="148"
            column="21"
            startOffset="6904"
            endLine="148"
            endColumn="50"
            endOffset="6933"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Upload Data&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_data_upload.xml"
            line="155"
            column="17"
            startOffset="7261"
            endLine="155"
            endColumn="43"
            endOffset="7287"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Add Expense&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_expense.xml"
            line="32"
            column="9"
            startOffset="1332"
            endLine="32"
            endColumn="35"
            endOffset="1358"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Search Invoice Track...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/fragment_update_delete.xml"
            line="27"
            column="9"
            startOffset="1056"
            endLine="27"
            endColumn="47"
            endOffset="1094"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;مشاريع نبراس الخوض للتجارة والمقاوالت&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/header_main.xml"
            line="17"
            column="13"
            startOffset="592"
            endLine="17"
            endColumn="65"
            endOffset="644"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;NABRAS AL- KHOUDH TRAD. &amp; CONT.PROJECTS&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/header_main.xml"
            line="29"
            column="13"
            startOffset="1084"
            endLine="29"
            endColumn="71"
            endOffset="1142"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;ص.ب:123,32الخوض ، سلطنة عمان، هاتف:99535325, س:1129470&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/header_main.xml"
            line="46"
            column="13"
            startOffset="1654"
            endLine="46"
            endColumn="82"
            endOffset="1723"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;P.O. BOX 32, 123Al - khoudh, sultanate of Oman, Tel:99535325 C.R. NO: 1129470&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/header_main.xml"
            line="57"
            column="13"
            startOffset="2123"
            endLine="57"
            endColumn="105"
            endOffset="2215"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;E-mail: <EMAIL>&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/header_main.xml"
            line="67"
            column="9"
            startOffset="2480"
            endLine="67"
            endColumn="47"
            endOffset="2518"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;QUOTATION&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/header_main.xml"
            line="77"
            column="9"
            startOffset="2837"
            endLine="77"
            endColumn="33"
            endOffset="2861"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Date and Time&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_data_invoice_traker.xml"
            line="19"
            column="9"
            startOffset="749"
            endLine="19"
            endColumn="37"
            endOffset="777"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Description&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_data_invoice_traker.xml"
            line="34"
            column="9"
            startOffset="1313"
            endLine="34"
            endColumn="35"
            endOffset="1339"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Location&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_data_invoice_traker.xml"
            line="44"
            column="9"
            startOffset="1664"
            endLine="44"
            endColumn="32"
            endOffset="1687"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;QR Code&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_data_invoice_traker.xml"
            line="57"
            column="9"
            startOffset="2132"
            endLine="57"
            endColumn="31"
            endOffset="2154"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;LPO&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_data_invoice_traker.xml"
            line="70"
            column="9"
            startOffset="2600"
            endLine="70"
            endColumn="27"
            endOffset="2618"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Invoice Number&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_data_invoice_traker.xml"
            line="83"
            column="9"
            startOffset="3064"
            endLine="83"
            endColumn="38"
            endOffset="3093"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Amount&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_data_invoice_traker.xml"
            line="96"
            column="9"
            startOffset="3542"
            endLine="96"
            endColumn="30"
            endOffset="3563"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;W/A&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_data_invoice_traker.xml"
            line="109"
            column="9"
            startOffset="4010"
            endLine="109"
            endColumn="27"
            endOffset="4028"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Payment Status&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_data_invoice_traker.xml"
            line="122"
            column="9"
            startOffset="4486"
            endLine="122"
            endColumn="38"
            endOffset="4515"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Edit&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_data_invoice_traker.xml"
            line="147"
            column="13"
            startOffset="5363"
            endLine="147"
            endColumn="32"
            endOffset="5382"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Delete&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_data_invoice_traker.xml"
            line="156"
            column="13"
            startOffset="5697"
            endLine="156"
            endColumn="34"
            endOffset="5718"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Category&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_expense.xml"
            line="12"
            column="9"
            startOffset="474"
            endLine="12"
            endColumn="32"
            endOffset="497"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Amount&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/item_expense.xml"
            line="21"
            column="9"
            startOffset="792"
            endLine="21"
            endColumn="30"
            endOffset="813"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Description&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/ivtraker_item_row.xml"
            line="13"
            column="9"
            startOffset="457"
            endLine="13"
            endColumn="35"
            endOffset="483"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Location&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/ivtraker_item_row.xml"
            line="23"
            column="9"
            startOffset="775"
            endLine="23"
            endColumn="32"
            endOffset="798"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;QR&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/ivtraker_item_row.xml"
            line="32"
            column="9"
            startOffset="1049"
            endLine="32"
            endColumn="26"
            endOffset="1066"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;LPO&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/ivtraker_item_row.xml"
            line="41"
            column="9"
            startOffset="1314"
            endLine="41"
            endColumn="27"
            endOffset="1332"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;INB&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/ivtraker_item_row.xml"
            line="50"
            column="9"
            startOffset="1580"
            endLine="50"
            endColumn="27"
            endOffset="1598"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Amount&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/ivtraker_item_row.xml"
            line="59"
            column="9"
            startOffset="1852"
            endLine="59"
            endColumn="30"
            endOffset="1873"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;W/A&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/ivtraker_item_row.xml"
            line="68"
            column="9"
            startOffset="2120"
            endLine="68"
            endColumn="27"
            endOffset="2138"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Update&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/ivtraker_item_row.xml"
            line="84"
            column="13"
            startOffset="2646"
            endLine="84"
            endColumn="34"
            endOffset="2667"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Delete&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/ivtraker_item_row.xml"
            line="92"
            column="13"
            startOffset="2926"
            endLine="92"
            endColumn="34"
            endOffset="2947"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Loading...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/loading_dialog.xml"
            line="22"
            column="9"
            startOffset="911"
            endLine="22"
            endColumn="34"
            endOffset="936"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Wallet Section&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/nav_menu.xml"
            line="4"
            column="11"
            startOffset="174"
            endLine="4"
            endColumn="41"
            endOffset="204"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Money Bag&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/nav_menu.xml"
            line="13"
            column="17"
            startOffset="493"
            endLine="13"
            endColumn="42"
            endOffset="518"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Invoice Design Section&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/nav_menu.xml"
            line="17"
            column="11"
            startOffset="565"
            endLine="17"
            endColumn="49"
            endOffset="603"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Invoice Design&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/nav_menu.xml"
            line="23"
            column="17"
            startOffset="823"
            endLine="23"
            endColumn="47"
            endOffset="853"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Worker Attendance Section&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/nav_menu.xml"
            line="27"
            column="11"
            startOffset="900"
            endLine="27"
            endColumn="52"
            endOffset="941"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Worker Attendance&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/nav_menu.xml"
            line="33"
            column="17"
            startOffset="1162"
            endLine="33"
            endColumn="50"
            endOffset="1195"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Finger Print Section&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/nav_menu.xml"
            line="37"
            column="11"
            startOffset="1242"
            endLine="37"
            endColumn="47"
            endOffset="1278"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Finger Print &quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/nav_menu.xml"
            line="43"
            column="17"
            startOffset="1492"
            endLine="43"
            endColumn="46"
            endOffset="1521"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;PDF Name&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/pdf_item.xml"
            line="30"
            column="17"
            startOffset="1147"
            endLine="30"
            endColumn="40"
            endOffset="1170"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Time and date&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/pdf_item.xml"
            line="47"
            column="21"
            startOffset="1862"
            endLine="47"
            endColumn="49"
            endOffset="1890"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;File Size&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/pdf_item.xml"
            line="59"
            column="21"
            startOffset="2403"
            endLine="59"
            endColumn="45"
            endOffset="2427"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;S.N&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table.xml"
            line="20"
            column="13"
            startOffset="697"
            endLine="20"
            endColumn="31"
            endOffset="715"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Description&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table.xml"
            line="38"
            column="13"
            startOffset="1334"
            endLine="38"
            endColumn="39"
            endOffset="1360"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;QTY&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table.xml"
            line="51"
            column="13"
            startOffset="1793"
            endLine="51"
            endColumn="31"
            endOffset="1811"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;UOM&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table.xml"
            line="64"
            column="13"
            startOffset="2248"
            endLine="64"
            endColumn="31"
            endOffset="2266"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Amount&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table.xml"
            line="78"
            column="13"
            startOffset="2737"
            endLine="78"
            endColumn="34"
            endOffset="2758"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;S.N&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table_two.xml"
            line="20"
            column="13"
            startOffset="697"
            endLine="20"
            endColumn="31"
            endOffset="715"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Description&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table_two.xml"
            line="38"
            column="13"
            startOffset="1334"
            endLine="38"
            endColumn="39"
            endOffset="1360"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;QTY&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table_two.xml"
            line="51"
            column="13"
            startOffset="1793"
            endLine="51"
            endColumn="31"
            endOffset="1811"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;UOM&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table_two.xml"
            line="64"
            column="13"
            startOffset="2248"
            endLine="64"
            endColumn="31"
            endOffset="2266"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Amount&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/table_two.xml"
            line="78"
            column="13"
            startOffset="2737"
            endLine="78"
            endColumn="34"
            endOffset="2758"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Total Amount Excluding 5% VAT&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo.xml"
            line="37"
            column="13"
            startOffset="1323"
            endLine="37"
            endColumn="57"
            endOffset="1367"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Total Amount Excluding 5% VAT&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/texinfo_two.xml"
            line="37"
            column="13"
            startOffset="1323"
            endLine="37"
            endColumn="57"
            endOffset="1367"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Convert Number to Words&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/text_to_number.xml"
            line="7"
            column="9"
            startOffset="282"
            endLine="7"
            endColumn="48"
            endOffset="321"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;INVOICE TRACKER&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/text_to_number.xml"
            line="12"
            column="9"
            startOffset="471"
            endLine="12"
            endColumn="40"
            endOffset="502"/>
    </incident>

</incidents>
