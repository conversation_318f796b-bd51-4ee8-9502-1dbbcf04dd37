1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.official.invoicegenarator"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:5:5-67
11-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
12-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:6:5-81
12-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:6:22-78
13    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
13-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:7:5-9:40
13-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:8:9-66
14    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" /> <!-- Need this for API 33 -->
14-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:10:5-80
14-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:10:22-77
15    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
15-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:11:5-76
15-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:11:22-73
16    <uses-permission android:name="android.permission.USE_BIOMETRIC" /> <!-- suppress DeprecatedClassUsageInspection -->
16-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2a8aa2878027e87434341bd924ace03\transformed\biometric-1.1.0\AndroidManifest.xml:24:5-72
16-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2a8aa2878027e87434341bd924ace03\transformed\biometric-1.1.0\AndroidManifest.xml:24:22-69
17    <uses-permission android:name="android.permission.USE_FINGERPRINT" /> <!-- Although the *SdkVersion is captured in gradle build files, this is required for non gradle builds -->
17-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2a8aa2878027e87434341bd924ace03\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
17-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\d2a8aa2878027e87434341bd924ace03\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
18    <!-- <uses-sdk android:minSdkVersion="21"/> -->
19    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
19-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b765ec09f16a5ac6a0a5e66ab1f912bd\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:22:5-79
19-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b765ec09f16a5ac6a0a5e66ab1f912bd\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:22:22-76
20    <uses-permission android:name="android.permission.WAKE_LOCK" />
20-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9ab80f88383b7964e93a8ff0ee42f678\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:24:5-68
20-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9ab80f88383b7964e93a8ff0ee42f678\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:24:22-65
21    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
21-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9ab80f88383b7964e93a8ff0ee42f678\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:25:5-79
21-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9ab80f88383b7964e93a8ff0ee42f678\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:25:22-76
22    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
22-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9ab80f88383b7964e93a8ff0ee42f678\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:26:5-88
22-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9ab80f88383b7964e93a8ff0ee42f678\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:26:22-85
23    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
23-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9ab80f88383b7964e93a8ff0ee42f678\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:27:5-82
23-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9ab80f88383b7964e93a8ff0ee42f678\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:27:22-79
24    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
24-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f13e1d7ad41e179a13cf86061b25b951\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:26:5-110
24-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f13e1d7ad41e179a13cf86061b25b951\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:26:22-107
25
26    <permission
26-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0168c57cfd1a8d4ed3c839d0e62e1137\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
27        android:name="com.official.invoicegenarator.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
27-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0168c57cfd1a8d4ed3c839d0e62e1137\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
28        android:protectionLevel="signature" />
28-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0168c57cfd1a8d4ed3c839d0e62e1137\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
29
30    <uses-permission android:name="com.official.invoicegenarator.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
30-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0168c57cfd1a8d4ed3c839d0e62e1137\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
30-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0168c57cfd1a8d4ed3c839d0e62e1137\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
31
32    <application
32-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:13:5-64:19
33        android:allowBackup="true"
33-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:14:9-35
34        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
34-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\0168c57cfd1a8d4ed3c839d0e62e1137\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
35        android:dataExtractionRules="@xml/data_extraction_rules"
35-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:15:9-65
36        android:extractNativeLibs="false"
37        android:fullBackupContent="@xml/backup_rules"
37-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:16:9-54
38        android:icon="@mipmap/ic_launcher"
38-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:17:9-43
39        android:label="@string/app_name"
39-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:18:9-41
40        android:networkSecurityConfig="@xml/network_security_config"
40-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:19:9-69
41        android:roundIcon="@mipmap/ic_launcher_round"
41-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:20:9-54
42        android:supportsRtl="true"
42-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:21:9-35
43        android:theme="@style/Theme.NewInvoice" >
43-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:22:9-48
44        <activity
44-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:24:9-26:40
45            android:name="com.official.invoicegenarator.InvoiceTwo"
45-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:25:13-39
46            android:exported="false" />
46-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:26:13-37
47        <activity
47-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:27:9-29:40
48            android:name="com.official.invoicegenarator.InvoiceTraker"
48-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:28:13-42
49            android:exported="false" />
49-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:29:13-37
50        <activity
50-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:30:9-32:40
51            android:name="com.official.invoicegenarator.PdfViewerActivity"
51-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:31:13-46
52            android:exported="false" />
52-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:32:13-37
53        <activity
53-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:33:9-35:40
54            android:name="com.official.invoicegenarator.DownloadListActivity"
54-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:34:13-49
55            android:exported="false" />
55-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:35:13-37
56        <activity
56-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:36:9-38:40
57            android:name="com.official.invoicegenarator.MoneyBagActivity"
57-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:37:13-45
58            android:exported="false" />
58-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:38:13-37
59        <activity
59-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:39:9-41:40
60            android:name="com.official.invoicegenarator.FingerprintSettingsActivity"
60-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:40:13-56
61            android:exported="false" />
61-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:41:13-37
62        <activity
62-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:42:9-44:40
63            android:name="com.official.invoicegenarator.VarifyActivity"
63-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:43:13-43
64            android:exported="false" />
64-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:44:13-37
65        <activity
65-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:45:9-47:40
66            android:name="com.official.invoicegenarator.WorkerAttendenceActivity"
66-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:46:13-53
67            android:exported="false" />
67-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:47:13-37
68        <activity
68-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:48:9-50:40
69            android:name="com.official.invoicegenarator.SelectionActivity"
69-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:49:13-46
70            android:exported="false" />
70-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:50:13-37
71        <activity
71-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:51:9-54:55
72            android:name="com.official.invoicegenarator.Home"
72-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:52:13-33
73            android:exported="false"
73-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:53:13-37
74            android:windowSoftInputMode="adjustPan" />
74-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:54:13-52
75        <activity
75-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:55:9-63:20
76            android:name="com.official.invoicegenarator.MainActivity"
76-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:56:13-41
77            android:exported="true" >
77-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:57:13-36
78            <intent-filter>
78-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:58:13-62:29
79                <action android:name="android.intent.action.MAIN" />
79-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:59:17-69
79-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:59:25-66
80
81                <category android:name="android.intent.category.LAUNCHER" />
81-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:61:17-77
81-->C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:61:27-74
82            </intent-filter>
83        </activity>
84        <activity
84-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\eb6dae60fd8fb9cee4fb7bcddf95903c\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:27:9-29:72
85            android:name="com.karumi.dexter.DexterActivity"
85-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\eb6dae60fd8fb9cee4fb7bcddf95903c\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:28:13-60
86            android:theme="@style/Dexter.Internal.Theme.Transparent" />
86-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\eb6dae60fd8fb9cee4fb7bcddf95903c\transformed\jetified-dexter-6.2.3\AndroidManifest.xml:29:13-69
87
88        <service
88-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b765ec09f16a5ac6a0a5e66ab1f912bd\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:26:9-35:19
89            android:name="com.google.firebase.components.ComponentDiscoveryService"
89-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b765ec09f16a5ac6a0a5e66ab1f912bd\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:27:13-84
90            android:directBootAware="true"
90-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\690ba2f772e2f392c9bd32f81534ff20\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
91            android:exported="false" >
91-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b765ec09f16a5ac6a0a5e66ab1f912bd\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:28:13-37
92            <meta-data
92-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b765ec09f16a5ac6a0a5e66ab1f912bd\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
93                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
93-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b765ec09f16a5ac6a0a5e66ab1f912bd\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:30:17-120
94                android:value="com.google.firebase.components.ComponentRegistrar" />
94-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b765ec09f16a5ac6a0a5e66ab1f912bd\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:31:17-82
95            <meta-data
95-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b765ec09f16a5ac6a0a5e66ab1f912bd\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
96                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
96-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b765ec09f16a5ac6a0a5e66ab1f912bd\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:33:17-109
97                android:value="com.google.firebase.components.ComponentRegistrar" />
97-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b765ec09f16a5ac6a0a5e66ab1f912bd\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:34:17-82
98            <meta-data
98-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\905ae480009aa267311a7d8e01d981fc\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:30:13-32:85
99                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
99-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\905ae480009aa267311a7d8e01d981fc\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:31:17-118
100                android:value="com.google.firebase.components.ComponentRegistrar" />
100-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\905ae480009aa267311a7d8e01d981fc\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:32:17-82
101            <meta-data
101-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\905ae480009aa267311a7d8e01d981fc\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:33:13-35:85
102                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
102-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\905ae480009aa267311a7d8e01d981fc\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:34:17-107
103                android:value="com.google.firebase.components.ComponentRegistrar" />
103-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\905ae480009aa267311a7d8e01d981fc\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:35:17-82
104            <meta-data
104-->[com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dc1290f4c450a51cff82df63825afd23\transformed\jetified-firebase-analytics-ktx-22.1.2\AndroidManifest.xml:11:13-13:85
105                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar"
105-->[com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dc1290f4c450a51cff82df63825afd23\transformed\jetified-firebase-analytics-ktx-22.1.2\AndroidManifest.xml:12:17-129
106                android:value="com.google.firebase.components.ComponentRegistrar" />
106-->[com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\dc1290f4c450a51cff82df63825afd23\transformed\jetified-firebase-analytics-ktx-22.1.2\AndroidManifest.xml:13:17-82
107            <meta-data
107-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b7895d036f18c6d1c36cc1ec53cdf476\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:25:13-27:85
108                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
108-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b7895d036f18c6d1c36cc1ec53cdf476\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:26:17-120
109                android:value="com.google.firebase.components.ComponentRegistrar" />
109-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b7895d036f18c6d1c36cc1ec53cdf476\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:27:17-82
110            <meta-data
110-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b7895d036f18c6d1c36cc1ec53cdf476\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:28:13-30:85
111                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
111-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b7895d036f18c6d1c36cc1ec53cdf476\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:29:17-117
112                android:value="com.google.firebase.components.ComponentRegistrar" />
112-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\b7895d036f18c6d1c36cc1ec53cdf476\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:30:17-82
113            <meta-data
113-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9ab80f88383b7964e93a8ff0ee42f678\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:37:13-39:85
114                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
114-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9ab80f88383b7964e93a8ff0ee42f678\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:38:17-139
115                android:value="com.google.firebase.components.ComponentRegistrar" />
115-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9ab80f88383b7964e93a8ff0ee42f678\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:39:17-82
116            <meta-data
116-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ad12a4bb62f7a6d050071bef23bbb374\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
117                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
117-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ad12a4bb62f7a6d050071bef23bbb374\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
118                android:value="com.google.firebase.components.ComponentRegistrar" />
118-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ad12a4bb62f7a6d050071bef23bbb374\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
119            <meta-data
119-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ad12a4bb62f7a6d050071bef23bbb374\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
120                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
120-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ad12a4bb62f7a6d050071bef23bbb374\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
121                android:value="com.google.firebase.components.ComponentRegistrar" />
121-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\ad12a4bb62f7a6d050071bef23bbb374\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
122            <meta-data
122-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\2667f3c10ff5005af88e6a4897006765\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
123                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
123-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\2667f3c10ff5005af88e6a4897006765\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
124                android:value="com.google.firebase.components.ComponentRegistrar" />
124-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\2667f3c10ff5005af88e6a4897006765\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
125            <meta-data
125-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\690ba2f772e2f392c9bd32f81534ff20\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
126                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
126-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\690ba2f772e2f392c9bd32f81534ff20\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
127                android:value="com.google.firebase.components.ComponentRegistrar" />
127-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\690ba2f772e2f392c9bd32f81534ff20\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
128        </service>
129
130        <property
130-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9ab80f88383b7964e93a8ff0ee42f678\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:30:9-32:61
131            android:name="android.adservices.AD_SERVICES_CONFIG"
131-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9ab80f88383b7964e93a8ff0ee42f678\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:31:13-65
132            android:resource="@xml/ga_ad_services_config" />
132-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\9ab80f88383b7964e93a8ff0ee42f678\transformed\jetified-play-services-measurement-api-22.1.2\AndroidManifest.xml:32:13-58
133
134        <provider
134-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\690ba2f772e2f392c9bd32f81534ff20\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
135            android:name="com.google.firebase.provider.FirebaseInitProvider"
135-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\690ba2f772e2f392c9bd32f81534ff20\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
136            android:authorities="com.official.invoicegenarator.firebaseinitprovider"
136-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\690ba2f772e2f392c9bd32f81534ff20\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
137            android:directBootAware="true"
137-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\690ba2f772e2f392c9bd32f81534ff20\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
138            android:exported="false"
138-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\690ba2f772e2f392c9bd32f81534ff20\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
139            android:initOrder="100" />
139-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\690ba2f772e2f392c9bd32f81534ff20\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
140
141        <receiver
141-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f13e1d7ad41e179a13cf86061b25b951\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:29:9-33:20
142            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
142-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f13e1d7ad41e179a13cf86061b25b951\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:30:13-85
143            android:enabled="true"
143-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f13e1d7ad41e179a13cf86061b25b951\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:31:13-35
144            android:exported="false" >
144-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f13e1d7ad41e179a13cf86061b25b951\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:32:13-37
145        </receiver>
146
147        <service
147-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f13e1d7ad41e179a13cf86061b25b951\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:35:9-38:40
148            android:name="com.google.android.gms.measurement.AppMeasurementService"
148-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f13e1d7ad41e179a13cf86061b25b951\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:36:13-84
149            android:enabled="true"
149-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f13e1d7ad41e179a13cf86061b25b951\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:37:13-35
150            android:exported="false" />
150-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f13e1d7ad41e179a13cf86061b25b951\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:38:13-37
151        <service
151-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f13e1d7ad41e179a13cf86061b25b951\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:39:9-43:72
152            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
152-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f13e1d7ad41e179a13cf86061b25b951\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:40:13-87
153            android:enabled="true"
153-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f13e1d7ad41e179a13cf86061b25b951\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:41:13-35
154            android:exported="false"
154-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f13e1d7ad41e179a13cf86061b25b951\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:42:13-37
155            android:permission="android.permission.BIND_JOB_SERVICE" />
155-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\f13e1d7ad41e179a13cf86061b25b951\transformed\jetified-play-services-measurement-22.1.2\AndroidManifest.xml:43:13-69
156
157        <activity
157-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\096f1df436aae94fbd41c57fb8e017a8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
158            android:name="com.google.android.gms.common.api.GoogleApiActivity"
158-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\096f1df436aae94fbd41c57fb8e017a8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
159            android:exported="false"
159-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\096f1df436aae94fbd41c57fb8e017a8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
160            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
160-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\096f1df436aae94fbd41c57fb8e017a8\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
161
162        <uses-library
162-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8976dce55b28e13ef82c0d0ace60d70a\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
163            android:name="android.ext.adservices"
163-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8976dce55b28e13ef82c0d0ace60d70a\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
164            android:required="false" />
164-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8976dce55b28e13ef82c0d0ace60d70a\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
165
166        <provider
166-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8fa9f103640828fc63b2ba63b777d76f\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
167            android:name="androidx.startup.InitializationProvider"
167-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8fa9f103640828fc63b2ba63b777d76f\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
168            android:authorities="com.official.invoicegenarator.androidx-startup"
168-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8fa9f103640828fc63b2ba63b777d76f\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
169            android:exported="false" >
169-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8fa9f103640828fc63b2ba63b777d76f\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
170            <meta-data
170-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8fa9f103640828fc63b2ba63b777d76f\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
171                android:name="androidx.emoji2.text.EmojiCompatInitializer"
171-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8fa9f103640828fc63b2ba63b777d76f\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
172                android:value="androidx.startup" />
172-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\8fa9f103640828fc63b2ba63b777d76f\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
173            <meta-data
173-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\162434658d2e3a566644b2cbbaabb9e7\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
174                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
174-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\162434658d2e3a566644b2cbbaabb9e7\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
175                android:value="androidx.startup" />
175-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\162434658d2e3a566644b2cbbaabb9e7\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
176            <meta-data
176-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c942f702031c0b4aaf5c9c8e584a2948\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
177                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
177-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c942f702031c0b4aaf5c9c8e584a2948\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
178                android:value="androidx.startup" />
178-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c942f702031c0b4aaf5c9c8e584a2948\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
179        </provider>
180
181        <meta-data
181-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\841427592dbdc5b54021fc73bff9e716\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
182            android:name="com.google.android.gms.version"
182-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\841427592dbdc5b54021fc73bff9e716\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
183            android:value="@integer/google_play_services_version" />
183-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\841427592dbdc5b54021fc73bff9e716\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
184
185        <receiver
185-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c942f702031c0b4aaf5c9c8e584a2948\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
186            android:name="androidx.profileinstaller.ProfileInstallReceiver"
186-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c942f702031c0b4aaf5c9c8e584a2948\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
187            android:directBootAware="false"
187-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c942f702031c0b4aaf5c9c8e584a2948\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
188            android:enabled="true"
188-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c942f702031c0b4aaf5c9c8e584a2948\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
189            android:exported="true"
189-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c942f702031c0b4aaf5c9c8e584a2948\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
190            android:permission="android.permission.DUMP" >
190-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c942f702031c0b4aaf5c9c8e584a2948\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
191            <intent-filter>
191-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c942f702031c0b4aaf5c9c8e584a2948\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
192                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
192-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c942f702031c0b4aaf5c9c8e584a2948\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
192-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c942f702031c0b4aaf5c9c8e584a2948\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
193            </intent-filter>
194            <intent-filter>
194-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c942f702031c0b4aaf5c9c8e584a2948\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
195                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
195-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c942f702031c0b4aaf5c9c8e584a2948\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
195-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c942f702031c0b4aaf5c9c8e584a2948\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
196            </intent-filter>
197            <intent-filter>
197-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c942f702031c0b4aaf5c9c8e584a2948\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
198                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
198-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c942f702031c0b4aaf5c9c8e584a2948\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
198-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c942f702031c0b4aaf5c9c8e584a2948\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
199            </intent-filter>
200            <intent-filter>
200-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c942f702031c0b4aaf5c9c8e584a2948\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
201                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
201-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c942f702031c0b4aaf5c9c8e584a2948\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
201-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.14.2\transforms\c942f702031c0b4aaf5c9c8e584a2948\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
202            </intent-filter>
203        </receiver>
204    </application>
205
206</manifest>
