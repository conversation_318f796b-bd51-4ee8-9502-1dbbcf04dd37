package com.official.invoicegenarator.models;

import com.google.gson.annotations.SerializedName;

/**
 * User model for authentication and user management
 */
public class User {
    
    @SerializedName("id")
    private int id;
    
    @SerializedName("name")
    private String name;
    
    @SerializedName("email")
    private String email;
    
    @SerializedName("role")
    private String role;
    
    @SerializedName("status")
    private int status;
    
    @SerializedName("last_login")
    private String lastLogin;
    
    // Constructors
    public User() {}
    
    public User(String name, String email, String role) {
        this.name = name;
        this.email = email;
        this.role = role;
        this.status = 1; // Active by default
    }
    
    // Getters and Setters
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public String getRole() {
        return role;
    }
    
    public void setRole(String role) {
        this.role = role;
    }
    
    public int getStatus() {
        return status;
    }
    
    public void setStatus(int status) {
        this.status = status;
    }
    
    public String getLastLogin() {
        return lastLogin;
    }
    
    public void setLastLogin(String lastLogin) {
        this.lastLogin = lastLogin;
    }
    
    // Helper methods
    public boolean isActive() {
        return status == 1;
    }
    
    public boolean isAdmin() {
        return "admin".equals(role);
    }
    
    public boolean isStaff() {
        return "staff".equals(role);
    }
    
    @Override
    public String toString() {
        return "User{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", email='" + email + '\'' +
                ", role='" + role + '\'' +
                ", status=" + status +
                '}';
    }
}
