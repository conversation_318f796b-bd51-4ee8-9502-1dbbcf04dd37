C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\SelectionActivity.java:158: Error: Overriding method should call super.onBackPressed [MissingSuperCall]
    public void onBackPressed() {
                ~~~~~~~~~~~~~

   Explanation for issues of type "MissingSuperCall":
   Some methods, such as View#onDetachedFromWindow, require that you also call
   the super implementation as part of your method.

C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\DownloadListActivity.java:209: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                if (pdfRef.getName().toLowerCase().contains(query.toLowerCase())) {
                                     ~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\DownloadListActivity.java:209: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                if (pdfRef.getName().toLowerCase().contains(query.toLowerCase())) {
                                                                  ~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\DownloadListActivity.java:403: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                return String.format("%.2f KB", sizeInBytes / 1024.0);
                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\DownloadListActivity.java:405: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                return String.format("%.2f MB", sizeInBytes / 1048576.0);
                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\expense\ExpenseAdapter.java:35: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        holder.tvAmount.setText(String.format("$%d", expense.getAmount()));
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\models\FileInfo.java:211: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        return fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
                                                                 ~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\models\FileInfo.java:221: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            return String.format("%.1f KB", fileSize / 1024.0);
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\models\FileInfo.java:223: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\models\FileInfo.java:225: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
            return String.format("%.1f GB", fileSize / (1024.0 * 1024.0 * 1024.0));
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\Home.java:830: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                        alertDialog.setMessage("Uploading... " + String.format("%.2f", progress) + "%");
                                                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\InvoiceTwo.java:698: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
                        alertDialog.setMessage("Uploading... " + String.format("%.2f", progress) + "%");
                                                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\UpdateDeleteFragment.java:193: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                if (item.getDescription().toLowerCase().contains(query.toLowerCase()) ||
                                          ~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\UpdateDeleteFragment.java:193: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                if (item.getDescription().toLowerCase().contains(query.toLowerCase()) ||
                                                                       ~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\UpdateDeleteFragment.java:194: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                        item.getLocation().toLowerCase().contains(query.toLowerCase()) ||
                                           ~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\UpdateDeleteFragment.java:194: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                        item.getLocation().toLowerCase().contains(query.toLowerCase()) ||
                                                                        ~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\UpdateDeleteFragment.java:195: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                        item.getQr().toLowerCase().contains(query.toLowerCase()) ||
                                     ~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\UpdateDeleteFragment.java:195: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                        item.getQr().toLowerCase().contains(query.toLowerCase()) ||
                                                                  ~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\UpdateDeleteFragment.java:196: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                        item.getLpo().toLowerCase().contains(query.toLowerCase()) ||
                                      ~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\UpdateDeleteFragment.java:196: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                        item.getLpo().toLowerCase().contains(query.toLowerCase()) ||
                                                                   ~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\UpdateDeleteFragment.java:197: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                        item.getInb().toLowerCase().contains(query.toLowerCase()) ||
                                      ~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\UpdateDeleteFragment.java:197: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                        item.getInb().toLowerCase().contains(query.toLowerCase()) ||
                                                                   ~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\UpdateDeleteFragment.java:198: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                        item.getCurrentDateTime().toLowerCase().contains(query.toLowerCase()) ||
                                                  ~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\UpdateDeleteFragment.java:198: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                        item.getCurrentDateTime().toLowerCase().contains(query.toLowerCase()) ||
                                                                               ~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\UpdateDeleteFragment.java:199: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                        item.getAmount().toLowerCase().contains(query.toLowerCase()) ||
                                         ~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\UpdateDeleteFragment.java:199: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                        item.getAmount().toLowerCase().contains(query.toLowerCase()) ||
                                                                      ~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\UpdateDeleteFragment.java:200: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                        item.getPaymentStatus().toLowerCase().contains(query.toLowerCase()) ||
                                                ~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\UpdateDeleteFragment.java:200: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                        item.getPaymentStatus().toLowerCase().contains(query.toLowerCase()) ||
                                                                             ~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\UpdateDeleteFragment.java:201: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                        item.getW_a().toLowerCase().contains(query.toLowerCase())) {
                                      ~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\UpdateDeleteFragment.java:201: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                        item.getW_a().toLowerCase().contains(query.toLowerCase())) {
                                                                   ~~~~~~~~~~~

   Explanation for issues of type "DefaultLocale":
   Calling String#toLowerCase() or #toUpperCase() without specifying an
   explicit locale is a common source of bugs. The reason for that is that
   those methods will use the current locale on the user's device, and even
   though the code appears to work correctly when you are developing the app,
   it will fail in some locales. For example, in the Turkish locale, the
   uppercase replacement for i is not I.

   If you want the methods to just perform ASCII replacement, for example to
   convert an enum name, call String#toUpperCase(Locale.ROOT) instead. If you
   really want to use the current locale, call
   String#toUpperCase(Locale.getDefault()) instead.

   https://developer.android.com/reference/java/util/Locale.html#default_locale

C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_home.xml:48: Warning: Duplicate id @+id/desc, defined or included multiple times in layout/activity_home.xml: [layout/activity_home.xml => layout/texmain.xml => layout/texinfo.xml defines @+id/desc, layout/activity_home.xml => layout/content_main.xml => layout/table.xml defines @+id/desc] [DuplicateIncludedIds]
            <include layout="@layout/texmain" />
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo.xml:26: Defined here, included via layout/activity_home.xml => layout/texmain.xml => layout/texinfo.xml defines @+id/desc
    C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table.xml:27: Defined here, included via layout/activity_home.xml => layout/content_main.xml => layout/table.xml defines @+id/desc
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_home.xml:48: Warning: Duplicate id @+id/serial, defined or included multiple times in layout/activity_home.xml: [layout/activity_home.xml => layout/texmain.xml => layout/texinfo.xml defines @+id/serial, layout/activity_home.xml => layout/content_main.xml => layout/table.xml defines @+id/serial] [DuplicateIncludedIds]
            <include layout="@layout/texmain" />
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo.xml:12: Defined here, included via layout/activity_home.xml => layout/texmain.xml => layout/texinfo.xml defines @+id/serial
    C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table.xml:12: Defined here, included via layout/activity_home.xml => layout/content_main.xml => layout/table.xml defines @+id/serial
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_invoice_two.xml:48: Warning: Duplicate id @+id/desc, defined or included multiple times in layout/activity_invoice_two.xml: [layout/activity_invoice_two.xml => layout/texmain_two.xml => layout/texinfo_two.xml defines @+id/desc, layout/activity_invoice_two.xml => layout/content_main_two.xml => layout/table_two.xml defines @+id/desc] [DuplicateIncludedIds]
            <include layout="@layout/texmain_two" />
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo_two.xml:26: Defined here, included via layout/activity_invoice_two.xml => layout/texmain_two.xml => layout/texinfo_two.xml defines @+id/desc
    C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table_two.xml:27: Defined here, included via layout/activity_invoice_two.xml => layout/content_main_two.xml => layout/table_two.xml defines @+id/desc
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_invoice_two.xml:48: Warning: Duplicate id @+id/serial, defined or included multiple times in layout/activity_invoice_two.xml: [layout/activity_invoice_two.xml => layout/texmain_two.xml => layout/texinfo_two.xml defines @+id/serial, layout/activity_invoice_two.xml => layout/content_main_two.xml => layout/table_two.xml defines @+id/serial] [DuplicateIncludedIds]
            <include layout="@layout/texmain_two" />
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo_two.xml:12: Defined here, included via layout/activity_invoice_two.xml => layout/texmain_two.xml => layout/texinfo_two.xml defines @+id/serial
    C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table_two.xml:12: Defined here, included via layout/activity_invoice_two.xml => layout/content_main_two.xml => layout/table_two.xml defines @+id/serial

   Explanation for issues of type "DuplicateIncludedIds":
   It's okay for two independent layouts to use the same ids. However, if
   layouts are combined with include tags, then the id's need to be unique
   within any chain of included layouts, or Activity#findViewById() can return
   an unexpected view.

C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\VarifyActivity.java:84: Warning: Field requires API level 30 (current min is 23): android.provider.Settings#ACTION_BIOMETRIC_ENROLL [InlinedApi]
            final Intent enrollIntent = new Intent(Settings.ACTION_BIOMETRIC_ENROLL);
                                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\VarifyActivity.java:85: Warning: Field requires API level 30 (current min is 23): android.provider.Settings#EXTRA_BIOMETRIC_AUTHENTICATORS_ALLOWED [InlinedApi]
            enrollIntent.putExtra(Settings.EXTRA_BIOMETRIC_AUTHENTICATORS_ALLOWED, BiometricManager.Authenticators.BIOMETRIC_STRONG);
                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "InlinedApi":
   This check scans through all the Android API field references in the
   application and flags certain constants, such as static final integers and
   Strings, which were introduced in later versions. These will actually be
   copied into the class files rather than being referenced, which means that
   the value is available even when running on older devices. In some cases
   that's fine, and in other cases it can result in a runtime crash or
   incorrect behavior. It depends on the context, so consider the code
   carefully and decide whether it's safe and can be suppressed or whether the
   code needs to be guarded.

   If you really want to use this API and don't need to support older devices
   just set the minSdkVersion in your build.gradle or AndroidManifest.xml
   files.

   If your code is deliberately accessing newer APIs, and you have ensured
   (e.g. with conditional execution) that this code will only ever be called
   on a supported platform, then you can annotate your class or method with
   the @TargetApi annotation specifying the local minimum SDK to apply, such
   as @TargetApi(11), such that this check considers 11 rather than your
   manifest file's minimum SDK as the required API level.

C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml: Error: When targeting Android 13 or higher, posting a permission requires holding the POST_NOTIFICATIONS permission (usage from com.bumptech.glide.request.target.NotificationTarget) [NotificationPermission]

   Explanation for issues of type "NotificationPermission":
   When targeting Android 13 and higher, posting permissions requires holding
   the runtime permission android.permission.POST_NOTIFICATIONS.

C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\expense\DatabaseHelper.java:89: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                int id = cursor.getInt(cursor.getColumnIndex(COLUMN_ID));
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\expense\DatabaseHelper.java:90: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                int amount = cursor.getInt(cursor.getColumnIndex(COLUMN_AMOUNT));
                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\expense\DatabaseHelper.java:91: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                String category = cursor.getString(cursor.getColumnIndex(COLUMN_CATEGORY));
                                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\expense\DatabaseHelper.java:92: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                String date = cursor.getString(cursor.getColumnIndex(COLUMN_DATE));
                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\expense\DatabaseHelper.java:93: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                String note = cursor.getString(cursor.getColumnIndex(COLUMN_NOTE));
                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\expense\DatabaseHelper.java:94: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                String type = cursor.getString(cursor.getColumnIndex(COLUMN_TYPE));
                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\expense\DatabaseHelper.java:120: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                int id = cursor.getInt(cursor.getColumnIndex(COLUMN_ID));
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\expense\DatabaseHelper.java:121: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                int amount = cursor.getInt(cursor.getColumnIndex(COLUMN_AMOUNT));
                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\expense\DatabaseHelper.java:122: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                String category = cursor.getString(cursor.getColumnIndex(COLUMN_CATEGORY));
                                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\expense\DatabaseHelper.java:123: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                String date = cursor.getString(cursor.getColumnIndex(COLUMN_DATE));
                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\expense\DatabaseHelper.java:124: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                String note = cursor.getString(cursor.getColumnIndex(COLUMN_NOTE));
                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\expense\DatabaseHelper.java:125: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                String type = cursor.getString(cursor.getColumnIndex(COLUMN_TYPE));
                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\expense\ExpenseFragment.java:69: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                int id = cursor.getInt(cursor.getColumnIndex("id")); // Get id from the cursor
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\expense\ExpenseFragment.java:70: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                String category = cursor.getString(cursor.getColumnIndex("category"));
                                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\expense\ExpenseFragment.java:71: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                int amount = cursor.getInt(cursor.getColumnIndex("amount"));
                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\expense\ExpenseFragment.java:74: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                String type = cursor.getString(cursor.getColumnIndex("type")); // Retrieve the type from the cursor
                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\expense\ExpenseFragment.java:91: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                String category = cursor.getString(cursor.getColumnIndex("category"));
                                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\expense\ExpenseFragment.java:92: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
                int amount = cursor.getInt(cursor.getColumnIndex("amount"));
                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\database\InvoiceDataHelper.java:327: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
        item.setLocalId(cursor.getString(cursor.getColumnIndex(COLUMN_LOCAL_ID)));
                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\database\InvoiceDataHelper.java:328: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
        item.setId(cursor.getString(cursor.getColumnIndex(COLUMN_ID)));
                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\database\InvoiceDataHelper.java:329: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
        item.setDescription(cursor.getString(cursor.getColumnIndex(COLUMN_DESCRIPTION)));
                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\database\InvoiceDataHelper.java:330: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
        item.setLocation(cursor.getString(cursor.getColumnIndex(COLUMN_LOCATION)));
                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\database\InvoiceDataHelper.java:331: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
        item.setQr(cursor.getString(cursor.getColumnIndex(COLUMN_QR)));
                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\database\InvoiceDataHelper.java:332: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
        item.setLpo(cursor.getString(cursor.getColumnIndex(COLUMN_LPO)));
                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\database\InvoiceDataHelper.java:333: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
        item.setInb(cursor.getString(cursor.getColumnIndex(COLUMN_INB)));
                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\database\InvoiceDataHelper.java:334: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
        item.setAmount(cursor.getString(cursor.getColumnIndex(COLUMN_AMOUNT)));
                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\database\InvoiceDataHelper.java:335: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
        item.setW_a(cursor.getString(cursor.getColumnIndex(COLUMN_W_A)));
                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\database\InvoiceDataHelper.java:336: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
        item.setPaymentStatus(cursor.getString(cursor.getColumnIndex(COLUMN_PAYMENT_STATUS)));
                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\database\InvoiceDataHelper.java:337: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
        item.setTimestamp(cursor.getLong(cursor.getColumnIndex(COLUMN_TIMESTAMP)));
                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\database\InvoiceDataHelper.java:338: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
        item.setCurrentDateTime(cursor.getString(cursor.getColumnIndex(COLUMN_CURRENT_DATE_TIME)));
                                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\database\InvoiceDataHelper.java:339: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
        item.setSynced(cursor.getInt(cursor.getColumnIndex(COLUMN_IS_SYNCED)) == 1);
                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\database\InvoiceDataHelper.java:340: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
        item.setLastSyncTime(cursor.getLong(cursor.getColumnIndex(COLUMN_LAST_SYNC_TIME)));
                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\database\InvoiceDataHelper.java:341: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
        item.setSyncStatus(cursor.getString(cursor.getColumnIndex(COLUMN_SYNC_STATUS)));
                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\database\InvoiceDataHelper.java:342: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
        item.setDeleted(cursor.getInt(cursor.getColumnIndex(COLUMN_IS_DELETED)) == 1);
                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\database\InvoiceDataHelper.java:343: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
        item.setCreatedAt(cursor.getLong(cursor.getColumnIndex(COLUMN_CREATED_AT)));
                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\database\InvoiceDataHelper.java:344: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
        item.setUpdatedAt(cursor.getLong(cursor.getColumnIndex(COLUMN_UPDATED_AT)));
                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\database\InvoiceDataHelper.java:414: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
        fileInfo.setId(cursor.getString(cursor.getColumnIndex(FILE_ID)));
                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\database\InvoiceDataHelper.java:415: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
        fileInfo.setOriginalName(cursor.getString(cursor.getColumnIndex(FILE_ORIGINAL_NAME)));
                                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\database\InvoiceDataHelper.java:416: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
        fileInfo.setStoredName(cursor.getString(cursor.getColumnIndex(FILE_STORED_NAME)));
                                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\database\InvoiceDataHelper.java:417: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
        fileInfo.setLocalPath(cursor.getString(cursor.getColumnIndex(FILE_LOCAL_PATH)));
                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\database\InvoiceDataHelper.java:418: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
        fileInfo.setRelativePath(cursor.getString(cursor.getColumnIndex(FILE_REMOTE_PATH)));
                                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\database\InvoiceDataHelper.java:419: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
        fileInfo.setFileSize(cursor.getLong(cursor.getColumnIndex(FILE_SIZE)));
                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\database\InvoiceDataHelper.java:420: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
        fileInfo.setFileType(cursor.getString(cursor.getColumnIndex(FILE_TYPE)));
                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\database\InvoiceDataHelper.java:421: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
        fileInfo.setFileExtension(cursor.getString(cursor.getColumnIndex(FILE_EXTENSION)));
                                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\database\InvoiceDataHelper.java:422: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
        fileInfo.setFileHash(cursor.getString(cursor.getColumnIndex(FILE_HASH)));
                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\database\InvoiceDataHelper.java:423: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
        fileInfo.setDownloaded(cursor.getInt(cursor.getColumnIndex(FILE_IS_DOWNLOADED)) == 1);
                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\database\InvoiceDataHelper.java:424: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
        fileInfo.setUploaded(cursor.getInt(cursor.getColumnIndex(FILE_IS_UPLOADED)) == 1);
                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\database\InvoiceDataHelper.java:425: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
        fileInfo.setDownloadTime(cursor.getLong(cursor.getColumnIndex(FILE_DOWNLOAD_TIME)));
                                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\database\InvoiceDataHelper.java:426: Error: Value must be ≥ 0 but getColumnIndex can be -1 [Range]
        fileInfo.setSyncStatus(cursor.getString(cursor.getColumnIndex(FILE_SYNC_STATUS)));
                                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "Range":
   Some parameters are required to be in a particular numerical range; this
   check makes sure that arguments passed fall within the range. For arrays,
   Strings and collections this refers to the size or length.

C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\menu\nav_menu.xml:5: Warning: Attribute iconTint is only used in API level 26 and higher (current min is 23) [UnusedAttribute]
        android:iconTint="@color/white"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\menu\nav_menu.xml:11: Warning: Attribute iconTint is only used in API level 26 and higher (current min is 23) [UnusedAttribute]
                android:iconTint="@color/white"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\menu\nav_menu.xml:21: Warning: Attribute iconTint is only used in API level 26 and higher (current min is 23) [UnusedAttribute]
                android:iconTint="@color/white"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\menu\nav_menu.xml:31: Warning: Attribute iconTint is only used in API level 26 and higher (current min is 23) [UnusedAttribute]
                android:iconTint="@color/white"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\menu\nav_menu.xml:41: Warning: Attribute iconTint is only used in API level 26 and higher (current min is 23) [UnusedAttribute]
                android:iconTint="@color/white"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UnusedAttribute":
   This check finds attributes set in XML files that were introduced in a
   version newer than the oldest version targeted by your application (with
   the minSdkVersion attribute).

   This is not an error; the application will simply ignore the attribute.
   However, if the attribute is important to the appearance or functionality
   of your application, you should consider finding an alternative way to
   achieve the same result with only available attributes, and then you can
   optionally create a copy of the layout in a layout-vNN folder which will be
   used on API NN or higher where you can take advantage of the newer
   attribute.

   Note: This check does not only apply to attributes. For example, some tags
   can be unused too, such as the new <tag> element in layouts introduced in
   API 21.

C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\Home.java:628: Warning: Avoid passing null as the view root (needed to resolve layout parameters on the inflated layout's root element) [InflateParams]
        View tableLayout = inflater.inflate(R.layout.item, null);
                                                           ~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\Home.java:651: Warning: Avoid passing null as the view root (needed to resolve layout parameters on the inflated layout's root element) [InflateParams]
        View tableLayout = inflater.inflate(R.layout.texinfo, null);
                                                              ~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\InvoiceTwo.java:523: Warning: Avoid passing null as the view root (needed to resolve layout parameters on the inflated layout's root element) [InflateParams]
        View tableLayout = inflater.inflate(R.layout.item_two, null);
                                                               ~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\InvoiceTwo.java:546: Warning: Avoid passing null as the view root (needed to resolve layout parameters on the inflated layout's root element) [InflateParams]
        View tableLayout = inflater.inflate(R.layout.texinfo_two, null);
                                                                  ~~~~

   Explanation for issues of type "InflateParams":
   When inflating a layout, avoid passing in null as the parent view, since
   otherwise any layout parameters on the root of the inflated layout will be
   ignored.

   https://www.bignerdranch.com/blog/understanding-androids-layoutinflater-inflate/

C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\expense\ExpenseFragment.java:112: Error: @layout/dialog_add_expense does not contain a declaration with id spCategory [MissingInflatedId]
        Spinner spCategory = dialogView.findViewById(R.id.spCategory);
                                                     ~~~~~~~~~~~~~~~

   Explanation for issues of type "MissingInflatedId":
   Checks calls to layout inflation and makes sure that the referenced ids are
   found in the corresponding layout (or at least one of them, if the layout
   has multiple configurations.)

C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\AndroidManifest.xml:11: Warning: Your app is currently not handling Selected Photos Access introduced in Android 14+ [SelectedPhotoAccess]
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "SelectedPhotoAccess":
   Selected Photo Access is a new ability for users to share partial access to
   their photo library when apps request access to their device storage on
   Android 14+.

   Instead of letting the system manage the selection lifecycle, we recommend
   you adapt your app to handle partial access to the photo library.

   https://developer.android.com/about/versions/14/changes/partial-photo-video-access

C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\download.xml:2: Warning: Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more [VectorRaster]
    android:width="512dp"
                   ~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\downloadlist.xml:2: Warning: Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more [VectorRaster]
    android:width="512dp"
                   ~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\upload.xml:2: Warning: Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more [VectorRaster]
    android:width="512dp"
                   ~~~~~

   Explanation for issues of type "VectorRaster":
   Vector icons require API 21 or API 24 depending on used features, but when
   minSdkVersion is less than 21 or 24 and Android Gradle plugin 1.4 or higher
   is used, a vector drawable placed in the drawable folder is automatically
   moved to drawable-anydpi-v21 or drawable-anydpi-v24 and bitmap images are
   generated for different screen resolutions for backwards compatibility.

   However, there are some limitations to this raster image generation, and
   this lint check flags elements and attributes that are not fully supported.
   You should manually check whether the generated output is acceptable for
   those older devices.

C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\gradle\libs.versions.toml:2: Warning: A newer version of com.android.application than 8.5.2 is available: 8.10.1 [AndroidGradlePluginVersion]
agp = "8.5.2"
      ~~~~~~~

   Explanation for issues of type "AndroidGradlePluginVersion":
   This detector looks for usage of the Android Gradle Plugin where the
   version you are using is not the current stable release. Using older
   versions is fine, and there are cases where you deliberately want to stick
   with an older version. However, you may simply not be aware that a more
   recent version is available, and that is what this lint check helps find.

C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\build.gradle:55: Warning: A newer version of com.google.firebase:firebase-bom than 33.4.0 is available: 33.15.0 [GradleDependency]
    implementation(platform("com.google.firebase:firebase-bom:33.4.0"))
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\build.gradle:57: Warning: A newer version of com.google.firebase:firebase-analytics-ktx than 22.1.2 is available: 22.4.0 [GradleDependency]
    implementation 'com.google.firebase:firebase-analytics-ktx:22.1.2' // Firebase Analytics (optional)
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\build.gradle:63: Warning: A newer version of com.google.code.gson:gson than 2.10.1 is available: 2.11.0 [GradleDependency]
    implementation 'com.google.code.gson:gson:2.10.1'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\build.gradle:67: Warning: A newer version of com.airbnb.android:lottie than 6.4.1 is available: 6.6.1 [GradleDependency]
    implementation 'com.airbnb.android:lottie:6.4.1'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\build.gradle:69: Warning: A newer version of com.github.bumptech.glide:glide than 4.12.0 is available: 4.16.0 [GradleDependency]
    implementation 'com.github.bumptech.glide:glide:4.12.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\build.gradle:70: Warning: A newer version of com.github.bumptech.glide:compiler than 4.12.0 is available: 4.16.0 [GradleDependency]
    annotationProcessor 'com.github.bumptech.glide:compiler:4.12.0'
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\gradle\libs.versions.toml:7: Warning: A newer version of androidx.appcompat:appcompat than 1.7.0 is available: 1.7.1 [GradleDependency]
appcompat = "1.7.0"
            ~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\gradle\libs.versions.toml:9: Warning: A newer version of androidx.activity:activity than 1.9.2 is available: 1.10.1 [GradleDependency]
activity = "1.9.2"
           ~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\gradle\libs.versions.toml:10: Warning: A newer version of androidx.constraintlayout:constraintlayout than 2.1.4 is available: 2.2.1 [GradleDependency]
constraintlayout = "2.1.4"
                   ~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\gradle\libs.versions.toml:11: Warning: A newer version of androidx.recyclerview:recyclerview than 1.3.2 is available: 1.4.0 [GradleDependency]
recyclerview = "1.3.2"
               ~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\gradle\libs.versions.toml:12: Warning: A newer version of androidx.core:core than 1.13.1 is available: 1.16.0 [GradleDependency]
core = "1.13.1"
       ~~~~~~~~

   Explanation for issues of type "GradleDependency":
   This detector looks for usages of libraries where the version you are using
   is not the current stable release. Using older versions is fine, and there
   are cases where you deliberately want to stick with an older version.
   However, you may simply not be aware that a more recent version is
   available, and that is what this lint check helps find.

C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\DownloadListActivity.java:163: Warning: It will always be more efficient to use more specific change events if you can. Rely on notifyDataSetChanged as a last resort. [NotifyDataSetChanged]
                pdfAdapter.notifyDataSetChanged();
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\DownloadListActivity.java:214: Warning: It will always be more efficient to use more specific change events if you can. Rely on notifyDataSetChanged as a last resort. [NotifyDataSetChanged]
        pdfAdapter.notifyDataSetChanged(); // Notify adapter for changes
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\expense\ExpenseFragment.java:81: Warning: It will always be more efficient to use more specific change events if you can. Rely on notifyDataSetChanged as a last resort. [NotifyDataSetChanged]
        expenseAdapter.notifyDataSetChanged();
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\expense\IncomeAdapter.java:45: Warning: It will always be more efficient to use more specific change events if you can. Rely on notifyDataSetChanged as a last resort. [NotifyDataSetChanged]
        notifyDataSetChanged();
        ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\UpdateDeleteFragment.java:170: Warning: It will always be more efficient to use more specific change events if you can. Rely on notifyDataSetChanged as a last resort. [NotifyDataSetChanged]
                dataAdapter.notifyDataSetChanged(); // Notify the adapter
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\UpdateDeleteFragment.java:345: Warning: It will always be more efficient to use more specific change events if you can. Rely on notifyDataSetChanged as a last resort. [NotifyDataSetChanged]
        notifyDataSetChanged();
        ~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "NotifyDataSetChanged":
   The RecyclerView adapter's onNotifyDataSetChanged method does not specify
   what about the data set has changed, forcing any observers to assume that
   all existing items and structure may no longer be valid. `LayoutManager`s
   will be forced to fully rebind and relayout all visible views.

C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\footer_main.xml:5: Warning: Invalid layout param 'layout_centerInParent' (included from within a LinearLayout in layout/activity_home.xml, included from within a LinearLayout in layout/activity_invoice_two.xml) [ObsoleteLayoutParam]
    android:layout_centerInParent="true"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ObsoleteLayoutParam":
   The given layout_param is not defined for the given layout, meaning it has
   no effect. This usually happens when you change the parent layout or move
   view code around without updating the layout params. This will cause
   useless attribute processing at runtime, and is misleading for others
   reading the layout so the parameter should be removed.

C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\values-night\themes.xml:14: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        <item name="android:statusBarColor" tools:targetApi="l">?attr/colorPrimaryVariant</item>
                                            ~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ObsoleteSdkInt":
   This check flags version checks that are not necessary, because the
   minSdkVersion (or surrounding known API level) is already at least as high
   as the version checked for.

   Similarly, it also looks for resources in -vNN folders, such as values-v14
   where the version qualifier is less than or equal to the minSdkVersion,
   where the contents should be merged into the best folder.

C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\baseline_settings_24.xml:3: Warning: Very long vector path (904 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
    <path android:fillColor="@android:color/white" android:pathData="M19.14,12.94c0.04,-0.3 0.06,-0.61 0.06,-0.94c0,-0.32 -0.02,-0.64 -0.07,-0.94l2.03,-1.58c0.18,-0.14 0.23,-0.41 0.12,-0.61l-1.92,-3.32c-0.12,-0.22 -0.37,-0.29 -0.59,-0.22l-2.39,0.96c-0.5,-0.38 -1.03,-0.7 -1.62,-0.94L14.4,2.81c-0.04,-0.24 -0.24,-0.41 -0.48,-0.41h-3.84c-0.24,0 -0.43,0.17 -0.47,0.41L9.25,5.35C8.66,5.59 8.12,5.92 7.63,6.29L5.24,5.33c-0.22,-0.08 -0.47,0 -0.59,0.22L2.74,8.87C2.62,9.08 2.66,9.34 2.86,9.48l2.03,1.58C4.84,11.36 4.8,11.69 4.8,12s0.02,0.64 0.07,0.94l-2.03,1.58c-0.18,0.14 -0.23,0.41 -0.12,0.61l1.92,3.32c0.12,0.22 0.37,0.29 0.59,0.22l2.39,-0.96c0.5,0.38 1.03,0.7 1.62,0.94l0.36,2.54c0.05,0.24 0.24,0.41 0.48,0.41h3.84c0.24,0 0.44,-0.17 0.47,-0.41l0.36,-2.54c0.59,-0.24 1.13,-0.56 1.62,-0.94l2.39,0.96c0.22,0.08 0.47,0 0.59,-0.22l1.92,-3.32c0.12,-0.22 0.07,-0.47 -0.12,-0.61L19.14,12.94zM12,15.6c-1.98,0 -3.6,-1.62 -3.6,-3.6s1.62,-3.6 3.6,-3.6s3.6,1.62 3.6,3.6S13.98,15.6 12,15.6z"/>
                                                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\downloadlist.xml:7: Warning: Very long vector path (1033 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
      android:pathData="M348.95,221.64v-96.89c0,-2.77 -1.28,-5.34 -3.09,-7.36L237.22,3.31C235.19,1.18 232.31,0 229.43,0L57.2,0C25.4,0 0,25.93 0,57.73v325.68c0,31.8 25.4,57.31 57.19,57.31h135.95C218.86,483.4 265.61,512 318.85,512c80.89,0 146.94,-65.73 146.94,-146.73 0.11,-70.75 -50.69,-129.87 -116.85,-143.63ZM240.1,37.46l72.88,76.72h-47.27c-14.09,0 -25.61,-11.63 -25.61,-25.72ZM57.19,419.38c-19.95,0 -35.85,-16.01 -35.85,-35.96L21.34,57.73c0,-20.06 15.9,-36.39 35.85,-36.39h161.56v67.12c0,25.93 21.02,47.06 46.95,47.06h61.89v83.34c-3.2,-0.11 -5.76,-0.43 -8.53,-0.43 -37.24,0 -71.5,14.3 -97.32,36.71L86.22,255.15c-5.87,0 -10.67,4.8 -10.67,10.67 0,5.87 4.8,10.67 10.67,10.67h115.68c-7.58,10.67 -13.88,21.34 -18.78,33.08L86.22,309.57c-5.87,0 -10.67,4.8 -10.67,10.67 0,5.87 4.8,10.67 10.67,10.67h89.96c-2.67,10.67 -4.05,22.52 -4.05,34.36 0,19.21 3.73,38.2 10.46,54.21L57.19,419.48ZM318.96,490.77c-69.15,0 -125.39,-56.24 -125.39,-125.39 0,-69.15 56.13,-125.39 125.39,-125.39 69.25,0 125.38,56.24 125.38,125.39 0,69.15 -56.24,125.39 -125.38,125.39ZM318.96,490.77"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\ic_accounts.xml:3: Warning: Very long vector path (848 characters), which is bad for performance. Considering reducing precision, removing minor details or rasterizing vector. [VectorPath]
    <path android:fillColor="#fff" android:fillType="evenOdd" android:pathData="M21,7C21,5.313 19.658,4.054 18.104,3.277C16.49,2.47 14.331,2 12,2C9.669,2 7.51,2.47 5.896,3.277C4.342,4.054 3,5.313 3,7V17C3,18.687 4.342,19.946 5.896,20.723C7.51,21.53 9.669,22 12,22C14.331,22 16.49,21.53 18.104,20.723C19.658,19.946 21,18.687 21,17V7ZM5.13,14.153C5.402,13.672 6.012,13.503 6.493,13.776C7.773,14.501 9.738,15 12,15C14.262,15 16.227,14.501 17.507,13.776C17.988,13.503 18.598,13.672 18.87,14.153C19.142,14.633 18.974,15.244 18.493,15.516C16.842,16.451 14.517,17 12,17C9.483,17 7.158,16.451 5.507,15.516C5.027,15.244 4.858,14.633 5.13,14.153ZM6.493,8.776C6.012,8.503 5.402,8.672 5.13,9.153C4.858,9.633 5.026,10.244 5.507,10.516C7.158,11.451 9.483,12 12,12C14.517,12 16.842,11.451 18.493,10.516C18.973,10.244 19.142,9.633 18.87,9.153C18.598,8.672 17.988,8.503 17.507,8.776C16.227,9.501 14.262,10 12,10C9.738,10 7.773,9.501 6.493,8.776Z"/>
                                                                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "VectorPath":
   Using long vector paths is bad for performance. There are several ways to
   make the pathData shorter:
   * Using less precision
   * Removing some minor details
   * Using the Android Studio vector conversion tool
   * Rasterizing the image (converting to PNG)

C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_home.xml:231: Warning: Use a layout_width of 0dp instead of 34dp for better performance [InefficientWeight]
                android:layout_width="34dp"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "InefficientWeight":
   When only a single widget in a LinearLayout defines a weight, it is more
   efficient to assign a width/height of 0dp to it since it will absorb all
   the remaining space anyway. With a declared width/height of 0dp it does not
   have to measure its own size first.

C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_download_list.xml:5: Warning: Possible overdraw: Root element paints background #2f3640 with a theme that also paints a background (inferred theme is @style/Theme.NewInvoice) [Overdraw]
    android:background="#2f3640"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_fingerprint_settings.xml:5: Warning: Possible overdraw: Root element paints background @drawable/fingerprintsetting with a theme that also paints a background (inferred theme is @style/Theme.NewInvoice) [Overdraw]
    android:background="@drawable/fingerprintsetting"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_main.xml:7: Warning: Possible overdraw: Root element paints background @drawable/mtcsplash with a theme that also paints a background (inferred theme is @style/Theme.NewInvoice) [Overdraw]
    android:background="@drawable/mtcsplash"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_varify.xml:5: Warning: Possible overdraw: Root element paints background @drawable/fingerback with a theme that also paints a background (inferred theme is @style/Theme.NewInvoice) [Overdraw]
    android:background="@drawable/fingerback"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\fgdata_upload.xml:12: Warning: Possible overdraw: Root element paints background @color/white with a theme that also paints a background (inferred theme is @style/Theme.NewInvoice) [Overdraw]
    android:background="@color/white">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\fragment_data_upload.xml:7: Warning: Possible overdraw: Root element paints background @drawable/iv_traker_upload with a theme that also paints a background (inferred theme is @style/Theme.NewInvoice) [Overdraw]
    android:background="@drawable/iv_traker_upload"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\fragment_update_delete.xml:5: Warning: Possible overdraw: Root element paints background @drawable/update_delete_iv_traker with a theme that also paints a background (inferred theme is @style/Theme.NewInvoice) [Overdraw]
    android:background="@drawable/update_delete_iv_traker"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\loading_dialog.xml:6: Warning: Possible overdraw: Root element paints background @android:color/white with a theme that also paints a background (inferred theme is @style/Theme.NewInvoice) [Overdraw]
    android:background="@android:color/white">
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "Overdraw":
   If you set a background drawable on a root view, then you should use a
   custom theme where the theme background is null. Otherwise, the theme
   background will be painted first, only to have your custom background
   completely cover it; this is called "overdraw".

   NOTE: This detector relies on figuring out which layouts are associated
   with which activities based on scanning the Java code, and it's currently
   doing that using an inexact pattern matching algorithm. Therefore, it can
   incorrectly conclude which activity the layout is associated with and then
   wrongly complain that a background-theme is hidden.

   If you want your custom background on multiple pages, then you should
   consider making a custom theme with your custom background and just using
   that theme instead of a root element background.

   Of course it's possible that your custom drawable is translucent and you
   want it to be mixed with the background. However, you will get better
   performance if you pre-mix the background with your drawable and use that
   resulting image or color as a custom theme background instead.

C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\accounts_bg.xml:2: Warning: The resource R.drawable.accounts_bg appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android">
^
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\backgroundwttop.xml:2: Warning: The resource R.drawable.backgroundwttop appears to be unused [UnusedResources]
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
^
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\category_bg.xml:2: Warning: The resource R.drawable.category_bg appears to be unused [UnusedResources]
<shape android:shape="oval" xmlns:android="http://schemas.android.com/apk/res/android">
^
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\raw\click_sound.mp3: Warning: The resource R.raw.click_sound appears to be unused [UnusedResources]
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\values\colors.xml:11: Warning: The resource R.color.orange appears to be unused [UnusedResources]
    <color name="orange">#F19802</color>
           ~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\values\colors.xml:13: Warning: The resource R.color.colorPrimary appears to be unused [UnusedResources]
    <color name="colorPrimary">#1976D2</color>
           ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\values\colors.xml:16: Warning: The resource R.color.colorPrimaryDark appears to be unused [UnusedResources]
    <color name="colorPrimaryDark">#3F51B5</color>
           ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\custom_horizontal_thumb.xml:1: Warning: The resource R.drawable.custom_horizontal_thumb appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android">
^
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\default_selector.xml:2: Warning: The resource R.drawable.default_selector appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android">
^
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\dialog_add_income.xml:2: Warning: The resource R.layout.dialog_add_income appears to be unused [UnusedResources]
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\dialog_bg.xml:2: Warning: The resource R.drawable.dialog_bg appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android">
^
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\dialog_progress.xml:2: Warning: The resource R.layout.dialog_progress appears to be unused [UnusedResources]
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\divider.xml:2: Warning: The resource R.drawable.divider appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android">
^
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\empty_state.png: Warning: The resource R.drawable.empty_state appears to be unused [UnusedResources]
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\expense_selector.xml:2: Warning: The resource R.drawable.expense_selector appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android">
^
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\ic_accounts.xml:1: Warning: The resource R.drawable.ic_accounts appears to be unused [UnusedResources]
<vector android:height="24dp" android:viewportHeight="24"
^
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\ic_book.xml:1: Warning: The resource R.drawable.ic_book appears to be unused [UnusedResources]
<vector android:height="24dp" android:viewportHeight="24"
^
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\ic_business.png: Warning: The resource R.drawable.ic_business appears to be unused [UnusedResources]
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\ic_chart.xml:1: Warning: The resource R.drawable.ic_chart appears to be unused [UnusedResources]
<vector android:height="24dp" android:viewportHeight="22"
^
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\ic_chevron.xml:1: Warning: The resource R.drawable.ic_chevron appears to be unused [UnusedResources]
<vector android:height="24dp" android:viewportHeight="24"
^
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\ic_investment.png: Warning: The resource R.drawable.ic_investment appears to be unused [UnusedResources]
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable-v24\ic_launcher_foreground.xml:1: Warning: The resource R.drawable.ic_launcher_foreground appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\ic_loan.png: Warning: The resource R.drawable.ic_loan appears to be unused [UnusedResources]
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\ic_more.xml:1: Warning: The resource R.drawable.ic_more appears to be unused [UnusedResources]
<vector android:height="24dp" android:viewportHeight="24"
^
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\ic_other.png: Warning: The resource R.drawable.ic_other appears to be unused [UnusedResources]
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\ic_plus.xml:1: Warning: The resource R.drawable.ic_plus appears to be unused [UnusedResources]
<vector android:height="24dp" android:viewportHeight="24"
^
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\ic_rent.png: Warning: The resource R.drawable.ic_rent appears to be unused [UnusedResources]
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\ic_salary.png: Warning: The resource R.drawable.ic_salary appears to be unused [UnusedResources]
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\ic_star.xml:1: Warning: The resource R.drawable.ic_star appears to be unused [UnusedResources]
<vector android:height="24dp" android:viewportHeight="24"
^
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\img.png: Warning: The resource R.drawable.img appears to be unused [UnusedResources]
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\income_selector.xml:2: Warning: The resource R.drawable.income_selector appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android">
^
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\invoice.png: Warning: The resource R.drawable.invoice appears to be unused [UnusedResources]
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\invoicelogo.png: Warning: The resource R.drawable.invoicelogo appears to be unused [UnusedResources]
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\ivtraker_item_row.xml:2: Warning: The resource R.layout.ivtraker_item_row appears to be unused [UnusedResources]
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\ltbback.xml:2: Warning: The resource R.drawable.ltbback appears to be unused [UnusedResources]
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
^
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\rightbgadd.xml:2: Warning: The resource R.drawable.rightbgadd appears to be unused [UnusedResources]
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
^
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\anim\rotate_in.xml:2: Warning: The resource R.anim.rotate_in appears to be unused [UnusedResources]
<rotate xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\anim\rotate_out.xml:2: Warning: The resource R.anim.rotate_out appears to be unused [UnusedResources]
<rotate xmlns:android="http://schemas.android.com/apk/res/android"
^
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\rtbback.xml:2: Warning: The resource R.drawable.rtbback appears to be unused [UnusedResources]
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
^
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\values\strings.xml:3: Warning: The resource R.string.company_name appears to be unused [UnusedResources]
    <string name="company_name">MTC Private L.T.D.</string>
            ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\values\strings.xml:4: Warning: The resource R.string.po_box appears to be unused [UnusedResources]
    <string name="po_box">P.O. BOX 32, 123</string>
            ~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\values\strings.xml:5: Warning: The resource R.string.address appears to be unused [UnusedResources]
    <string name="address">AI-khoudh, Sultanate of Oman</string>
            ~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\values\strings.xml:6: Warning: The resource R.string.phone appears to be unused [UnusedResources]
    <string name="phone">Tel: 99535325</string>
            ~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\values\strings.xml:7: Warning: The resource R.string.cr_no appears to be unused [UnusedResources]
    <string name="cr_no">C.R. NO: 1129470</string>
            ~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\values\strings.xml:8: Warning: The resource R.string.email appears to be unused [UnusedResources]
    <string name="email"><EMAIL></string>
            ~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\values\strings.xml:14: Warning: The resource R.string.quotation appears to be unused [UnusedResources]
    <string name="quotation">QUOTATION</string>
            ~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\values\strings.xml:15: Warning: The resource R.string.subject appears to be unused [UnusedResources]
    <string name="subject">SUBJECT: shifting of chairs to Batinah Region</string>
            ~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\values\strings.xml:16: Warning: The resource R.string.description appears to be unused [UnusedResources]
    <string name="description">Description</string>
            ~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\values\strings.xml:17: Warning: The resource R.string.date appears to be unused [UnusedResources]
    <string name="date">Date</string>
            ~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\values\strings.xml:18: Warning: The resource R.string.qr_no appears to be unused [UnusedResources]
    <string name="qr_no">QR No</string>
            ~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\values\strings.xml:19: Warning: The resource R.string.location appears to be unused [UnusedResources]
    <string name="location">Location</string>
            ~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\values\strings.xml:20: Warning: The resource R.string.vat_in appears to be unused [UnusedResources]
    <string name="vat_in">VAT IN</string>
            ~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\values\strings.xml:21: Warning: The resource R.string.qty appears to be unused [UnusedResources]
    <string name="qty">QTY</string>
            ~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\values\strings.xml:22: Warning: The resource R.string.uom appears to be unused [UnusedResources]
    <string name="uom">UOM</string>
            ~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\values\strings.xml:23: Warning: The resource R.string.unit_price appears to be unused [UnusedResources]
    <string name="unit_price">Unit price</string>
            ~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\values\strings.xml:24: Warning: The resource R.string.amount appears to be unused [UnusedResources]
    <string name="amount">Amount</string>
            ~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\values\strings.xml:25: Warning: The resource R.string.omr appears to be unused [UnusedResources]
    <string name="omr">OMR</string>
            ~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\values\strings.xml:26: Warning: The resource R.string.barka appears to be unused [UnusedResources]
    <string name="barka">Barka 2 shifting of 8 customers &amp; 8 staff chairs</string>
            ~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\values\strings.xml:27: Warning: The resource R.string.mussina appears to be unused [UnusedResources]
    <string name="mussina">Mussina 2 shifting of 8 chairs staff chairs</string>
            ~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\values\strings.xml:28: Warning: The resource R.string.suwaiq appears to be unused [UnusedResources]
    <string name="suwaiq">Suwaiq 2 shifting of 6 staff chairs</string>
            ~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\values\strings.xml:29: Warning: The resource R.string.saham appears to be unused [UnusedResources]
    <string name="saham">Saham 2 shifting of 1 customer &amp; 3 staff chairs</string>
            ~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\values\strings.xml:30: Warning: The resource R.string.sohar_industrial appears to be unused [UnusedResources]
    <string name="sohar_industrial">Sohar Industrial shifting of 4 customers &amp; 4 staff chairs</string>
            ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\values\strings.xml:31: Warning: The resource R.string.wadi_hibi appears to be unused [UnusedResources]
    <string name="wadi_hibi">Wadi Hibi shifting of 4 customers &amp; 4 staff chairs</string>
            ~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\values\strings.xml:32: Warning: The resource R.string.sininah appears to be unused [UnusedResources]
    <string name="sininah">Sininah 2 coatomers &amp; 1 staff chair</string>
            ~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\values\strings.xml:33: Warning: The resource R.string.burimi appears to be unused [UnusedResources]
    <string name="burimi">Burimi 3 staff chairs and 3 tables</string>
            ~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\values\strings.xml:34: Warning: The resource R.string.mhada appears to be unused [UnusedResources]
    <string name="mhada">Mhada 5 customers</string>
            ~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\values\strings.xml:35: Warning: The resource R.string.total_amount_excluding_vat appears to be unused [UnusedResources]
    <string name="total_amount_excluding_vat">Total Amount Excluding 5% VAT</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\values\strings.xml:36: Warning: The resource R.string.add_vat appears to be unused [UnusedResources]
    <string name="add_vat">Add: 5%VAT</string>
            ~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\values\strings.xml:37: Warning: The resource R.string.total_amount_including_vat appears to be unused [UnusedResources]
    <string name="total_amount_including_vat">Total Amount including 5% VAT</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\values\strings.xml:38: Warning: The resource R.string.total_amount appears to be unused [UnusedResources]
    <string name="total_amount">Total amount RO 556.5/- (Rial Omani Five Hundred-Fifty-Six &amp; Five Hundred-Baisa-Only)</string>
            ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\values\strings.xml:40: Warning: The resource R.string.default_off_text appears to be unused [UnusedResources]
    <string name="default_off_text">Off</string>
            ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\values\strings.xml:41: Warning: The resource R.string.default_on_text appears to be unused [UnusedResources]
    <string name="default_on_text">On</string>
            ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\values\strings.xml:43: Warning: The resource R.string.hello_blank_fragment appears to be unused [UnusedResources]
    <string name="hello_blank_fragment">Hello blank fragment</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\underline.xml:2: Warning: The resource R.drawable.underline appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android">
^
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\vertical_border_black.xml:2: Warning: The resource R.layout.vertical_border_black appears to be unused [UnusedResources]
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
^

   Explanation for issues of type "UnusedResources":
   Unused resources make applications larger and slow down builds.


   The unused resource check can ignore tests. If you want to include
   resources that are only referenced from tests, consider packaging them in a
   test source set instead.

   You can include test sources in the unused resource check by setting the
   system property lint.unused-resources.include-tests =true, and to exclude
   them (usually for performance reasons), use
   lint.unused-resources.exclude-tests =true.
   ,

   Available options:

   **skip-libraries** (default is true):
   Whether the unused resource check should skip reporting unused resources in libraries.

   Many libraries will declare resources that are part of the library surface; other modules depending on the library will also reference the resources. To avoid reporting all these resources as unused (in the context of a library), the unused resource check normally skips reporting unused resources in libraries. Instead, run the unused resource check on the consuming app module (along with `checkDependencies=true`).

   However, there are cases where you want to check that all the resources declared in a library are used; in that case, you can disable the skip option.

   To configure this option, use a `lint.xml` file with an <option> like this:

   ```xml
   <lint>
       <issue id="UnusedResources">
           <option name="skip-libraries" value="true" />
       </issue>
   </lint>
   ```

C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_download_list.xml:15: Warning: This RadioGroup layout or its LinearLayout parent is possibly unnecessary; transfer the background attribute to the other view [UselessParent]
        <RadioGroup
         ~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:221: Warning: This LinearLayout layout or its LinearLayout parent is unnecessary [UselessParent]
            <LinearLayout
             ~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:221: Warning: This LinearLayout layout or its LinearLayout parent is unnecessary [UselessParent]
            <LinearLayout
             ~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item.xml:7: Warning: This TableRow layout or its TableLayout parent is unnecessary [UselessParent]
    <TableRow
     ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item_two.xml:7: Warning: This TableRow layout or its TableLayout parent is unnecessary [UselessParent]
    <TableRow
     ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\nav_header.xml:11: Warning: This LinearLayout layout or its LinearLayout parent is unnecessary; transfer the background attribute to the other view [UselessParent]
    <LinearLayout
     ~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\pdf_item.xml:17: Warning: This LinearLayout layout or its RelativeLayout parent is unnecessary; transfer the background attribute to the other view [UselessParent]
        <LinearLayout
         ~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table.xml:7: Warning: This TableRow layout or its TableLayout parent is unnecessary [UselessParent]
    <TableRow
     ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table_two.xml:7: Warning: This TableRow layout or its TableLayout parent is unnecessary [UselessParent]
    <TableRow
     ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo.xml:7: Warning: This TableRow layout or its TableLayout parent is unnecessary [UselessParent]
    <TableRow
     ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo_two.xml:7: Warning: This TableRow layout or its TableLayout parent is unnecessary [UselessParent]
    <TableRow
     ~~~~~~~~

   Explanation for issues of type "UselessParent":
   A layout with children that has no siblings, is not a scrollview or a root
   layout, and does not have a background, can be removed and have its
   children moved directly into the parent for a flatter and more efficient
   layout hierarchy.

C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\converter_document.png: Warning: Action Bar icons should use a single gray color (#333333 for light themes (with 60%/30% opacity for enabled/disabled), and #FFFFFF with opacity 80%/30% for dark themes [IconColors]
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\invoice_traker.png: Warning: Action Bar icons should use a single gray color (#333333 for light themes (with 60%/30% opacity for enabled/disabled), and #FFFFFF with opacity 80%/30% for dark themes [IconColors]

   Explanation for issues of type "IconColors":
   Notification icons and Action Bar icons should only white and shades of
   gray. See the Android Design Guide for more details. Note that the way Lint
   decides whether an icon is an action bar icon or a notification icon is
   based on the filename prefix: ic_menu_ for action bar icons, ic_stat_ for
   notification icons etc. These correspond to the naming conventions
   documented in
   https://d.android.com/r/studio-ui/designer/material/iconography

C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml:2: Warning: The application adaptive icon is missing a monochrome tag [MonochromeLauncherIcon]
<adaptive-icon xmlns:android="http://schemas.android.com/apk/res/android">
^
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml:2: Warning: The application adaptive roundIcon is missing a monochrome tag [MonochromeLauncherIcon]
<adaptive-icon xmlns:android="http://schemas.android.com/apk/res/android">
^

   Explanation for issues of type "MonochromeLauncherIcon":
   If android:roundIcon and android:icon are both in your manifest, you must
   either remove the reference to android:roundIcon if it is not needed; or,
   supply the monochrome icon in the drawable defined by the android:roundIcon
   and android:icon attribute.

   For example, if android:roundIcon and android:icon are both in the
   manifest, a launcher might choose to use android:roundIcon over
   android:icon to display the adaptive app icon. Therefore, your themed
   application iconwill not show if your monochrome attribute is not also
   specified in android:roundIcon.

C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\converter_document.png: Warning: Found bitmap drawable res/drawable/converter_document.png in densityless folder [IconLocation]
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\delete.png: Warning: Found bitmap drawable res/drawable/delete.png in densityless folder [IconLocation]
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\empty_state.png: Warning: Found bitmap drawable res/drawable/empty_state.png in densityless folder [IconLocation]
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\fingerback.png: Warning: Found bitmap drawable res/drawable/fingerback.png in densityless folder [IconLocation]
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\fingerprint.png: Warning: Found bitmap drawable res/drawable/fingerprint.png in densityless folder [IconLocation]
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\fingerprintsetting.png: Warning: Found bitmap drawable res/drawable/fingerprintsetting.png in densityless folder [IconLocation]
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\fingerprintthree.png: Warning: Found bitmap drawable res/drawable/fingerprintthree.png in densityless folder [IconLocation]
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\fingerprinttwo.png: Warning: Found bitmap drawable res/drawable/fingerprinttwo.png in densityless folder [IconLocation]
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\first_invoice.png: Warning: Found bitmap drawable res/drawable/first_invoice.png in densityless folder [IconLocation]
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\ic_business.png: Warning: Found bitmap drawable res/drawable/ic_business.png in densityless folder [IconLocation]
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\ic_investment.png: Warning: Found bitmap drawable res/drawable/ic_investment.png in densityless folder [IconLocation]
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\ic_loan.png: Warning: Found bitmap drawable res/drawable/ic_loan.png in densityless folder [IconLocation]
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\ic_other.png: Warning: Found bitmap drawable res/drawable/ic_other.png in densityless folder [IconLocation]
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\ic_rent.png: Warning: Found bitmap drawable res/drawable/ic_rent.png in densityless folder [IconLocation]
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\ic_salary.png: Warning: Found bitmap drawable res/drawable/ic_salary.png in densityless folder [IconLocation]
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\img.png: Warning: Found bitmap drawable res/drawable/img.png in densityless folder [IconLocation]
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\invoice.png: Warning: Found bitmap drawable res/drawable/invoice.png in densityless folder [IconLocation]
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\invoice_traker.png: Warning: Found bitmap drawable res/drawable/invoice_traker.png in densityless folder [IconLocation]
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\invoicelogo.png: Warning: Found bitmap drawable res/drawable/invoicelogo.png in densityless folder [IconLocation]
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\iv_traker_upload.png: Warning: Found bitmap drawable res/drawable/iv_traker_upload.png in densityless folder [IconLocation]
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\moneybag.png: Warning: Found bitmap drawable res/drawable/moneybag.png in densityless folder [IconLocation]
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\mtcsplash.png: Warning: Found bitmap drawable res/drawable/mtcsplash.png in densityless folder [IconLocation]
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\navigationicon.png: Warning: Found bitmap drawable res/drawable/navigationicon.png in densityless folder [IconLocation]
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\second_invoice.png: Warning: Found bitmap drawable res/drawable/second_invoice.png in densityless folder [IconLocation]
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\signature.png: Warning: Found bitmap drawable res/drawable/signature.png in densityless folder [IconLocation]
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\update_delete_iv_traker.png: Warning: Found bitmap drawable res/drawable/update_delete_iv_traker.png in densityless folder [IconLocation]
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\view.png: Warning: Found bitmap drawable res/drawable/view.png in densityless folder [IconLocation]
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\drawable\workerattendance.png: Warning: Found bitmap drawable res/drawable/workerattendance.png in densityless folder [IconLocation]

   Explanation for issues of type "IconLocation":
   The res/drawable folder is intended for density-independent graphics such
   as shapes defined in XML. For bitmaps, move it to drawable-mdpi and
   consider providing higher and lower resolution versions in drawable-ldpi,
   drawable-hdpi and drawable-xhdpi. If the icon really is density independent
   (for example a solid color) you can place it in drawable-nodpi.

   https://developer.android.com/guide/practices/screens_support.html

C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\custom_file_name_dialog.xml:49: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
        <Button
         ~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\custom_file_name_dialog.xml:59: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
        <Button
         ~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\dialog_exit_confirmation.xml:47: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
            <Button
             ~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\dialog_exit_confirmation.xml:54: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
            <Button
             ~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\ivtraker_item_row.xml:80: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
        <Button
         ~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\ivtraker_item_row.xml:88: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
        <Button
         ~~~~~~

   Explanation for issues of type "ButtonStyle":
   Button bars typically use a borderless style for the buttons. Set the
   style="?android:attr/buttonBarButtonStyle" attribute on each of the
   buttons, and set style="?android:attr/buttonBarStyle" on the parent layout

   https://d.android.com/r/studio-ui/designer/material/dialogs

C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\banklay.xml:9: Warning: This text field does not specify an inputType [TextFields]
    <EditText
     ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\banklay.xml:32: Warning: This text field does not specify an inputType [TextFields]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\banklay.xml:46: Warning: This text field does not specify an inputType [TextFields]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\banklay.xml:65: Warning: This text field does not specify an inputType [TextFields]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\banklay.xml:79: Warning: This text field does not specify an inputType [TextFields]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\banklay.xml:98: Warning: This text field does not specify an inputType [TextFields]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\banklay.xml:112: Warning: This text field does not specify an inputType [TextFields]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:25: Warning: This text field does not specify an inputType [TextFields]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:42: Warning: This text field does not specify an inputType [TextFields]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:61: Warning: This text field does not specify an inputType [TextFields]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:79: Warning: This text field does not specify an inputType [TextFields]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:90: Warning: This text field does not specify an inputType [TextFields]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:101: Warning: This text field does not specify an inputType [TextFields]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:131: Warning: This text field does not specify an inputType [TextFields]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:149: Warning: This text field does not specify an inputType [TextFields]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:169: Warning: This text field does not specify an inputType [TextFields]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:188: Warning: This text field does not specify an inputType [TextFields]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:226: Warning: This text field does not specify an inputType [TextFields]
                <EditText
                 ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:25: Warning: This text field does not specify an inputType [TextFields]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:42: Warning: This text field does not specify an inputType [TextFields]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:61: Warning: This text field does not specify an inputType [TextFields]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:79: Warning: This text field does not specify an inputType [TextFields]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:90: Warning: This text field does not specify an inputType [TextFields]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:101: Warning: This text field does not specify an inputType [TextFields]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:131: Warning: This text field does not specify an inputType [TextFields]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:149: Warning: This text field does not specify an inputType [TextFields]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:169: Warning: This text field does not specify an inputType [TextFields]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:188: Warning: This text field does not specify an inputType [TextFields]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:226: Warning: This text field does not specify an inputType [TextFields]
                <EditText
                 ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\dialog_add_expense.xml:15: Warning: This text field does not specify an inputType [TextFields]
    <EditText
     ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\dialog_add_income.xml:19: Warning: This text field does not specify an inputType [TextFields]
    <EditText
     ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\footer_main.xml:8: Warning: This text field does not specify an inputType [TextFields]
    <EditText
     ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\header_main.xml:13: Warning: This text field does not specify an inputType [TextFields]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\header_main.xml:23: Warning: This text field does not specify an inputType [TextFields]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\header_main.xml:41: Warning: This text field does not specify an inputType [TextFields]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\header_main.xml:51: Warning: This text field does not specify an inputType [TextFields]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\header_main.xml:64: Warning: This text field does not specify an inputType [TextFields]
    <EditText
     ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\header_main.xml:74: Warning: This text field does not specify an inputType [TextFields]
    <EditText
     ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item.xml:11: Warning: This text field does not specify an inputType [TextFields]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item.xml:24: Warning: This text field does not specify an inputType [TextFields]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item.xml:36: Warning: This text field does not specify an inputType [TextFields]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item.xml:48: Warning: This text field does not specify an inputType [TextFields]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item.xml:60: Warning: This text field does not specify an inputType [TextFields]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item_two.xml:11: Warning: This text field does not specify an inputType [TextFields]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item_two.xml:24: Warning: This text field does not specify an inputType [TextFields]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item_two.xml:36: Warning: This text field does not specify an inputType [TextFields]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item_two.xml:48: Warning: This text field does not specify an inputType [TextFields]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item_two.xml:60: Warning: This text field does not specify an inputType [TextFields]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table.xml:11: Warning: This text field does not specify an inputType [TextFields]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table.xml:26: Warning: This text field does not specify an inputType [TextFields]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table.xml:43: Warning: This text field does not specify an inputType [TextFields]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table.xml:57: Warning: This text field does not specify an inputType [TextFields]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table.xml:70: Warning: This text field does not specify an inputType [TextFields]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table_two.xml:11: Warning: This text field does not specify an inputType [TextFields]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table_two.xml:26: Warning: This text field does not specify an inputType [TextFields]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table_two.xml:43: Warning: This text field does not specify an inputType [TextFields]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table_two.xml:57: Warning: This text field does not specify an inputType [TextFields]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table_two.xml:70: Warning: This text field does not specify an inputType [TextFields]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo.xml:11: Warning: This text field does not specify an inputType [TextFields]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo.xml:25: Warning: This text field does not specify an inputType [TextFields]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo.xml:42: Warning: This text field does not specify an inputType [TextFields]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo.xml:55: Warning: This text field does not specify an inputType [TextFields]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo.xml:67: Warning: This text field does not specify an inputType [TextFields]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo_two.xml:11: Warning: This text field does not specify an inputType [TextFields]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo_two.xml:25: Warning: This text field does not specify an inputType [TextFields]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo_two.xml:42: Warning: This text field does not specify an inputType [TextFields]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo_two.xml:55: Warning: This text field does not specify an inputType [TextFields]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo_two.xml:67: Warning: This text field does not specify an inputType [TextFields]
        <EditText
         ~~~~~~~~

   Explanation for issues of type "TextFields":
   Providing an inputType attribute on a text field improves usability because
   depending on the data to be input, optimized keyboards can be shown to the
   user (such as just digits and parentheses for a phone number). 

   The lint detector also looks at the id of the view, and if the id offers a
   hint of the purpose of the field (for example, the id contains the phrase
   phone or email), then lint will also ensure that the inputType contains the
   corresponding type attributes.

   If you really want to keep the text field generic, you can suppress this
   warning by setting inputType="text".

C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:33: Warning: Avoid using sizes smaller than 11sp: 8sp [SmallSp]
                android:textSize="8sp"
                ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:50: Warning: Avoid using sizes smaller than 11sp: 8sp [SmallSp]
                android:textSize="8sp"
                ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:69: Warning: Avoid using sizes smaller than 11sp: 8sp [SmallSp]
                android:textSize="8sp"
                ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:87: Warning: Avoid using sizes smaller than 11sp: 8sp [SmallSp]
                android:textSize="8sp"
                ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:98: Warning: Avoid using sizes smaller than 11sp: 8sp [SmallSp]
                android:textSize="8sp"
                ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:109: Warning: Avoid using sizes smaller than 11sp: 8sp [SmallSp]
                android:textSize="8sp"
                ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:140: Warning: Avoid using sizes smaller than 11sp: 8sp [SmallSp]
                android:textSize="8sp"
                ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:158: Warning: Avoid using sizes smaller than 11sp: 8sp [SmallSp]
                android:textSize="8sp"
                ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:177: Warning: Avoid using sizes smaller than 11sp: 8sp [SmallSp]
                android:textSize="8sp"
                ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:196: Warning: Avoid using sizes smaller than 11sp: 8sp [SmallSp]
                android:textSize="8sp"
                ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:234: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
                    android:textSize="10sp"
                    ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:33: Warning: Avoid using sizes smaller than 11sp: 8sp [SmallSp]
                android:textSize="8sp"
                ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:50: Warning: Avoid using sizes smaller than 11sp: 8sp [SmallSp]
                android:textSize="8sp"
                ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:69: Warning: Avoid using sizes smaller than 11sp: 8sp [SmallSp]
                android:textSize="8sp"
                ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:87: Warning: Avoid using sizes smaller than 11sp: 8sp [SmallSp]
                android:textSize="8sp"
                ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:98: Warning: Avoid using sizes smaller than 11sp: 8sp [SmallSp]
                android:textSize="8sp"
                ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:109: Warning: Avoid using sizes smaller than 11sp: 8sp [SmallSp]
                android:textSize="8sp"
                ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:140: Warning: Avoid using sizes smaller than 11sp: 8sp [SmallSp]
                android:textSize="8sp"
                ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:158: Warning: Avoid using sizes smaller than 11sp: 8sp [SmallSp]
                android:textSize="8sp"
                ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:177: Warning: Avoid using sizes smaller than 11sp: 8sp [SmallSp]
                android:textSize="8sp"
                ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:196: Warning: Avoid using sizes smaller than 11sp: 8sp [SmallSp]
                android:textSize="8sp"
                ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:234: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
                    android:textSize="10sp"
                    ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\footer_main.xml:17: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
        android:textSize="10sp"
        ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\header_main.xml:19: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
            android:textSize="10sp"
            ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\header_main.xml:30: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
            android:textSize="10sp"
            ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\header_main.xml:47: Warning: Avoid using sizes smaller than 11sp: 8sp [SmallSp]
            android:textSize="8sp"
            ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\header_main.xml:58: Warning: Avoid using sizes smaller than 11sp: 8sp [SmallSp]
            android:textSize="8sp"
            ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\header_main.xml:71: Warning: Avoid using sizes smaller than 11sp: 9sp [SmallSp]
        android:textSize="9sp"
        ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item.xml:22: Warning: Avoid using sizes smaller than 11sp: 9sp [SmallSp]
            android:textSize="9sp"/>
            ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item.xml:34: Warning: Avoid using sizes smaller than 11sp: 9sp [SmallSp]
            android:textSize="9sp"/>
            ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item.xml:46: Warning: Avoid using sizes smaller than 11sp: 9sp [SmallSp]
            android:textSize="9sp"/>
            ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item.xml:58: Warning: Avoid using sizes smaller than 11sp: 9sp [SmallSp]
            android:textSize="9sp"/>
            ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item.xml:71: Warning: Avoid using sizes smaller than 11sp: 9sp [SmallSp]
            android:textSize="9sp" />
            ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item_two.xml:22: Warning: Avoid using sizes smaller than 11sp: 9sp [SmallSp]
            android:textSize="9sp"/>
            ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item_two.xml:34: Warning: Avoid using sizes smaller than 11sp: 9sp [SmallSp]
            android:textSize="9sp"/>
            ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item_two.xml:46: Warning: Avoid using sizes smaller than 11sp: 9sp [SmallSp]
            android:textSize="9sp"/>
            ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item_two.xml:58: Warning: Avoid using sizes smaller than 11sp: 9sp [SmallSp]
            android:textSize="9sp"/>
            ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item_two.xml:71: Warning: Avoid using sizes smaller than 11sp: 9sp [SmallSp]
            android:textSize="9sp" />
            ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table.xml:22: Warning: Avoid using sizes smaller than 11sp: 9sp [SmallSp]
            android:textSize="9sp"
            ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table.xml:40: Warning: Avoid using sizes smaller than 11sp: 9sp [SmallSp]
            android:textSize="9sp"
            ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table.xml:53: Warning: Avoid using sizes smaller than 11sp: 9sp [SmallSp]
            android:textSize="9sp"
            ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table.xml:66: Warning: Avoid using sizes smaller than 11sp: 9sp [SmallSp]
            android:textSize="9sp"
            ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table.xml:80: Warning: Avoid using sizes smaller than 11sp: 9sp [SmallSp]
            android:textSize="9sp"
            ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table_two.xml:22: Warning: Avoid using sizes smaller than 11sp: 9sp [SmallSp]
            android:textSize="9sp"
            ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table_two.xml:40: Warning: Avoid using sizes smaller than 11sp: 9sp [SmallSp]
            android:textSize="9sp"
            ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table_two.xml:53: Warning: Avoid using sizes smaller than 11sp: 9sp [SmallSp]
            android:textSize="9sp"
            ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table_two.xml:66: Warning: Avoid using sizes smaller than 11sp: 9sp [SmallSp]
            android:textSize="9sp"
            ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table_two.xml:80: Warning: Avoid using sizes smaller than 11sp: 9sp [SmallSp]
            android:textSize="9sp"
            ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo.xml:22: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
            android:textSize="10sp"
            ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo.xml:40: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
            android:textSize="10sp"/>
            ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo.xml:53: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
            android:textSize="10sp"/>
            ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo.xml:65: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
            android:textSize="10sp"/>
            ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo.xml:78: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
            android:textSize="10sp"/>
            ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo_two.xml:22: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
            android:textSize="10sp"
            ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo_two.xml:40: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
            android:textSize="10sp"/>
            ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo_two.xml:53: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
            android:textSize="10sp"/>
            ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo_two.xml:65: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
            android:textSize="10sp"/>
            ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo_two.xml:78: Warning: Avoid using sizes smaller than 11sp: 10sp [SmallSp]
            android:textSize="10sp"/>
            ~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "SmallSp":
   Avoid using sizes smaller than 11sp.

C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_download_list.xml:37: Warning: Missing autofillHints attribute [Autofill]
    <EditText
     ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\banklay.xml:9: Warning: Missing autofillHints attribute [Autofill]
    <EditText
     ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\banklay.xml:32: Warning: Missing autofillHints attribute [Autofill]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\banklay.xml:46: Warning: Missing autofillHints attribute [Autofill]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\banklay.xml:65: Warning: Missing autofillHints attribute [Autofill]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\banklay.xml:79: Warning: Missing autofillHints attribute [Autofill]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\banklay.xml:98: Warning: Missing autofillHints attribute [Autofill]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\banklay.xml:112: Warning: Missing autofillHints attribute [Autofill]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:25: Warning: Missing autofillHints attribute [Autofill]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:42: Warning: Missing autofillHints attribute [Autofill]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:61: Warning: Missing autofillHints attribute [Autofill]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:79: Warning: Missing autofillHints attribute [Autofill]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:90: Warning: Missing autofillHints attribute [Autofill]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:101: Warning: Missing autofillHints attribute [Autofill]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:131: Warning: Missing autofillHints attribute [Autofill]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:149: Warning: Missing autofillHints attribute [Autofill]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:169: Warning: Missing autofillHints attribute [Autofill]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:188: Warning: Missing autofillHints attribute [Autofill]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:226: Warning: Missing autofillHints attribute [Autofill]
                <EditText
                 ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:25: Warning: Missing autofillHints attribute [Autofill]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:42: Warning: Missing autofillHints attribute [Autofill]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:61: Warning: Missing autofillHints attribute [Autofill]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:79: Warning: Missing autofillHints attribute [Autofill]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:90: Warning: Missing autofillHints attribute [Autofill]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:101: Warning: Missing autofillHints attribute [Autofill]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:131: Warning: Missing autofillHints attribute [Autofill]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:149: Warning: Missing autofillHints attribute [Autofill]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:169: Warning: Missing autofillHints attribute [Autofill]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:188: Warning: Missing autofillHints attribute [Autofill]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:226: Warning: Missing autofillHints attribute [Autofill]
                <EditText
                 ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\dialog_add_expense.xml:8: Warning: Missing autofillHints attribute [Autofill]
    <EditText
     ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\dialog_add_expense.xml:15: Warning: Missing autofillHints attribute [Autofill]
    <EditText
     ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\dialog_add_expense.xml:21: Warning: Missing autofillHints attribute [Autofill]
    <EditText
     ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\dialog_add_expense.xml:29: Warning: Missing autofillHints attribute [Autofill]
    <EditText
     ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\dialog_add_income.xml:9: Warning: Missing autofillHints attribute [Autofill]
    <EditText
     ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\dialog_add_income.xml:19: Warning: Missing autofillHints attribute [Autofill]
    <EditText
     ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\footer_main.xml:8: Warning: Missing autofillHints attribute [Autofill]
    <EditText
     ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\fragment_update_delete.xml:19: Warning: Missing autofillHints attribute [Autofill]
    <EditText
     ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\header_main.xml:13: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\header_main.xml:23: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\header_main.xml:41: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\header_main.xml:51: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\header_main.xml:64: Warning: Missing autofillHints attribute [Autofill]
    <EditText
     ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\header_main.xml:74: Warning: Missing autofillHints attribute [Autofill]
    <EditText
     ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item.xml:11: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item.xml:24: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item.xml:36: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item.xml:48: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item.xml:60: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item_two.xml:11: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item_two.xml:24: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item_two.xml:36: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item_two.xml:48: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item_two.xml:60: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table.xml:11: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table.xml:26: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table.xml:43: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table.xml:57: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table.xml:70: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table_two.xml:11: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table_two.xml:26: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table_two.xml:43: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table_two.xml:57: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table_two.xml:70: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo.xml:11: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo.xml:25: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo.xml:42: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo.xml:55: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo.xml:67: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo_two.xml:11: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo_two.xml:25: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo_two.xml:42: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo_two.xml:55: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo_two.xml:67: Warning: Missing autofillHints attribute [Autofill]
        <EditText
         ~~~~~~~~

   Explanation for issues of type "Autofill":
   Specify an autofillHints attribute when targeting SDK version 26 or higher
   or explicitly specify that the view is not important for autofill. Your app
   can help an autofill service classify the data correctly by providing the
   meaning of each view that could be autofillable, such as views representing
   usernames, passwords, credit card fields, email addresses, etc.

   The hints can have any value, but it is recommended to use predefined
   values like 'username' for a username or 'creditCardNumber' for a credit
   card number. For a list of all predefined autofill hint constants, see the
   AUTOFILL_HINT_ constants in the View reference at
   https://developer.android.com/reference/android/view/View.html.

   You can mark a view unimportant for autofill by specifying an
   importantForAutofill attribute on that view or a parent view. See
   https://developer.android.com/reference/android/view/View.html#setImportant
   ForAutofill(int).

   https://developer.android.com/guide/topics/text/autofill.html

C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\build.gradle:51: Warning: Use version catalog instead [UseTomlInstead]
    implementation 'com.itextpdf:itextpdf:5.5.12'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\build.gradle:53: Warning: Use version catalog instead [UseTomlInstead]
    implementation 'com.karumi:dexter:6.2.3'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\build.gradle:55: Warning: Use version catalog instead [UseTomlInstead]
    implementation(platform("com.google.firebase:firebase-bom:33.4.0"))
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\build.gradle:56: Warning: Use version catalog instead [UseTomlInstead]
    implementation 'com.google.firebase:firebase-database:21.0.0' // Firebase Realtime Database
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\build.gradle:57: Warning: Use version catalog instead [UseTomlInstead]
    implementation 'com.google.firebase:firebase-analytics-ktx:22.1.2' // Firebase Analytics (optional)
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\build.gradle:61: Warning: Use version catalog instead [UseTomlInstead]
    implementation("com.google.firebase:firebase-storage")
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\build.gradle:62: Warning: Use version catalog instead [UseTomlInstead]
    implementation 'androidx.biometric:biometric:1.1.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\build.gradle:63: Warning: Use version catalog instead [UseTomlInstead]
    implementation 'com.google.code.gson:gson:2.10.1'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\build.gradle:64: Warning: Use version catalog instead [UseTomlInstead]
    implementation 'com.github.zcweng:switch-button:0.0.3@aar'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\build.gradle:66: Warning: Use version catalog instead [UseTomlInstead]
    implementation 'com.github.PhilJay:MPAndroidChart:v3.1.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\build.gradle:67: Warning: Use version catalog instead [UseTomlInstead]
    implementation 'com.airbnb.android:lottie:6.4.1'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\build.gradle:69: Warning: Use version catalog instead [UseTomlInstead]
    implementation 'com.github.bumptech.glide:glide:4.12.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\build.gradle:70: Warning: Use version catalog instead [UseTomlInstead]
    annotationProcessor 'com.github.bumptech.glide:compiler:4.12.0'
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\build.gradle:73: Warning: Use version catalog instead [UseTomlInstead]
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\build.gradle:74: Warning: Use version catalog instead [UseTomlInstead]
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\build.gradle:75: Warning: Use version catalog instead [UseTomlInstead]
    implementation 'com.squareup.okhttp3:okhttp-urlconnection:4.12.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UseTomlInstead":
   If your project is using a libs.versions.toml file, you should place all
   Gradle dependencies in the TOML file. This lint check looks for version
   declarations outside of the TOML file and suggests moving them (and in the
   IDE, provides a quickfix to performing the operation automatically).

C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\CustomButtonEffect.java:44: Warning: onTouch should call View#performClick when a click is detected [ClickableViewAccessibility]
            public boolean onTouch(View v, MotionEvent motionEvent) {
                           ~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\Home.java:207: Warning: Custom view `LinearLayout` has setOnTouchListener called on it but does not override performClick [ClickableViewAccessibility]
        sllayout.setOnTouchListener(new View.OnTouchListener() {
        ^
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\Home.java:209: Warning: onTouch should call View#performClick when a click is detected [ClickableViewAccessibility]
            public boolean onTouch(View v, MotionEvent event) {
                           ~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\InvoiceTwo.java:196: Warning: Custom view `LinearLayout` has setOnTouchListener called on it but does not override performClick [ClickableViewAccessibility]
        sllayout.setOnTouchListener(new View.OnTouchListener() {
        ^
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\InvoiceTwo.java:198: Warning: onTouch should call View#performClick when a click is detected [ClickableViewAccessibility]
            public boolean onTouch(View v, MotionEvent event) {
                           ~~~~~~~

   Explanation for issues of type "ClickableViewAccessibility":
   If a View that overrides onTouchEvent or uses an OnTouchListener does not
   also implement performClick and call it when clicks are detected, the View
   may not handle accessibility actions properly. Logic handling the click
   actions should ideally be placed in View#performClick as some accessibility
   services invoke performClick when a click action should occur.

C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_home.xml:116: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_home.xml:124: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_home.xml:144: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_home.xml:151: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_home.xml:158: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_home.xml:167: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_home.xml:176: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_home.xml:185: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_home.xml:203: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_home.xml:212: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_home.xml:229: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_invoice_two.xml:102: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_invoice_two.xml:110: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_invoice_two.xml:130: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_invoice_two.xml:137: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_invoice_two.xml:144: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_invoice_two.xml:153: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_invoice_two.xml:162: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_invoice_two.xml:171: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_invoice_two.xml:189: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_invoice_two.xml:198: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_selection.xml:53: Warning: Missing contentDescription attribute on image [ContentDescription]
                    <ImageView
                     ~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_selection.xml:74: Warning: Missing contentDescription attribute on image [ContentDescription]
                    <ImageView
                     ~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_selection.xml:102: Warning: Missing contentDescription attribute on image [ContentDescription]
                        <ImageView
                         ~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_selection.xml:122: Warning: Missing contentDescription attribute on image [ContentDescription]
                        <ImageView
                         ~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_selection.xml:145: Warning: Missing contentDescription attribute on image [ContentDescription]
                    <ImageView
                     ~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_varify.xml:9: Warning: Missing contentDescription attribute on image [ContentDescription]
    <ImageView
     ~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\fgdata_upload.xml:167: Warning: Missing contentDescription attribute on image [ContentDescription]
                <ImageButton
                 ~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\nav_header.xml:17: Warning: Missing contentDescription attribute on image [ContentDescription]
        <ImageView
         ~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\pdf_item.xml:71: Warning: Missing contentDescription attribute on image [ContentDescription]
                <ImageView
                 ~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\pdf_item.xml:78: Warning: Missing contentDescription attribute on image [ContentDescription]
                <ImageView
                 ~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\pdf_item.xml:85: Warning: Missing contentDescription attribute on image [ContentDescription]
                <ImageView
                 ~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\signature_layout.xml:7: Warning: Missing contentDescription attribute on image [ContentDescription]
    <ImageView
     ~~~~~~~~~

   Explanation for issues of type "ContentDescription":
   Non-textual widgets like ImageViews and ImageButtons should use the
   contentDescription attribute to specify a textual description of the widget
   such that screen readers and other accessibility tools can adequately
   describe the user interface.

   Note that elements in application screens that are purely decorative and do
   not provide any content or enable a user action should not have
   accessibility content descriptions. In this case, set their descriptions to
   @null. If your app's minSdkVersion is 16 or higher, you can instead set
   these graphical elements' android:importantForAccessibility attributes to
   no.

   Note that for text fields, you should not set both the hint and the
   contentDescription attributes since the hint will never be shown. Just set
   the hint.

   https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases

C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\banklay.xml:9: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
    <EditText
     ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\banklay.xml:32: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\banklay.xml:46: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\banklay.xml:65: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\banklay.xml:79: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\banklay.xml:98: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\banklay.xml:112: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:25: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:42: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:61: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:79: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:90: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:101: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:131: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:149: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:169: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:188: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:25: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:42: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:61: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:79: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:90: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:101: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:131: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:149: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:169: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:188: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
            <EditText
             ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\footer_main.xml:8: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
    <EditText
     ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\header_main.xml:13: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\header_main.xml:23: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\header_main.xml:41: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\header_main.xml:51: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\header_main.xml:64: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
    <EditText
     ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\header_main.xml:74: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
    <EditText
     ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item.xml:11: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item.xml:24: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item.xml:36: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item.xml:48: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item.xml:60: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item_two.xml:11: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item_two.xml:24: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item_two.xml:36: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item_two.xml:48: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item_two.xml:60: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table.xml:11: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table.xml:26: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table.xml:43: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table.xml:57: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table.xml:70: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table_two.xml:11: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table_two.xml:26: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table_two.xml:43: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table_two.xml:57: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table_two.xml:70: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo.xml:11: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo.xml:25: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo.xml:42: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo.xml:55: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo.xml:67: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo_two.xml:11: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo_two.xml:25: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo_two.xml:42: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo_two.xml:55: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
        <EditText
         ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo_two.xml:67: Warning: Missing accessibility label: provide either a view with an android:labelFor that references this view or provide an android:hint [LabelFor]
        <EditText
         ~~~~~~~~

   Explanation for issues of type "LabelFor":
   Editable text fields should provide an android:hint or, provided your
   minSdkVersion is at least 17, they may be referenced by a view with a
   android:labelFor attribute.

   When using android:labelFor, be sure to provide an android:text or an
   android:contentDescription.

   If your view is labeled but by a label in a different layout which includes
   this one, just suppress this warning from lint.

C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\DownloadListActivity.java:264: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                holder.pdfTimestamp.setText("Unknown date"); // Handle error
                                            ~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\DownloadListActivity.java:265: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                holder.pdfSize.setText("Unknown size"); // Handle error for size
                                       ~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\Home.java:135: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        date.setText("Date: "+currentDate);
                     ~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\Home.java:135: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        date.setText("Date: "+currentDate);
                     ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\Home.java:140: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        qrTextView.setText("QR No : QR " + newcurrentDate + "SIB");
                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\Home.java:140: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        qrTextView.setText("QR No : QR " + newcurrentDate + "SIB");
                           ~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\Home.java:140: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        qrTextView.setText("QR No : QR " + newcurrentDate + "SIB");
                                                            ~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\Home.java:433: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        textViewWords.setText("Invalid number");
                                              ~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\Home.java:566: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        exitTitle.setText("Are you sure you want to close First Invoice?");
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\Home.java:745: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            titleView.setText("Enter file name");
                              ~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\InvoiceTwo.java:130: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        date.setText("Date: "+currentDate);
                     ~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\InvoiceTwo.java:130: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        date.setText("Date: "+currentDate);
                     ~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\InvoiceTwo.java:334: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
       exitTitle.setText("Are you sure you want to close Second Invoice?");
                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\InvoiceTwo.java:385: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                        textViewWords.setText("Total amount RO :-" + number + "/-"+" (Rial Omani " + numberInWords + " rial-Only)");
                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\InvoiceTwo.java:385: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        textViewWords.setText("Total amount RO :-" + number + "/-"+" (Rial Omani " + numberInWords + " rial-Only)");
                                              ~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\InvoiceTwo.java:385: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        textViewWords.setText("Total amount RO :-" + number + "/-"+" (Rial Omani " + numberInWords + " rial-Only)");
                                                                                   ~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\InvoiceTwo.java:385: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        textViewWords.setText("Total amount RO :-" + number + "/-"+" (Rial Omani " + numberInWords + " rial-Only)");
                                                                                                                     ~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\InvoiceTwo.java:388: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                        textViewWords.setText("Invalid number");
                                              ~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\InvoiceTwo.java:613: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            titleView.setText("Enter file name");
                              ~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\UpdateDeleteFragment.java:323: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        holder.textViewDescription.setText("Description: " + item.getDescription());
                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\UpdateDeleteFragment.java:323: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        holder.textViewDescription.setText("Description: " + item.getDescription());
                                           ~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\UpdateDeleteFragment.java:324: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        holder.textViewLocation.setText("Location: " + item.getLocation());
                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\UpdateDeleteFragment.java:324: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        holder.textViewLocation.setText("Location: " + item.getLocation());
                                        ~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\UpdateDeleteFragment.java:325: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        holder.textViewQr.setText("QR Code: " + item.getQr());
                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\UpdateDeleteFragment.java:325: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        holder.textViewQr.setText("QR Code: " + item.getQr());
                                  ~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\UpdateDeleteFragment.java:326: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        holder.textViewLpo.setText("LPO: " + item.getLpo());
                                   ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\UpdateDeleteFragment.java:326: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        holder.textViewLpo.setText("LPO: " + item.getLpo());
                                   ~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\UpdateDeleteFragment.java:327: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        holder.textViewInb.setText("Invoice Number: " + item.getInb());
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\UpdateDeleteFragment.java:327: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        holder.textViewInb.setText("Invoice Number: " + item.getInb());
                                   ~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\UpdateDeleteFragment.java:328: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        holder.textViewAmount.setText("Amount: " + item.getAmount());
                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\UpdateDeleteFragment.java:328: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        holder.textViewAmount.setText("Amount: " + item.getAmount());
                                      ~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\UpdateDeleteFragment.java:329: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        holder.textViewW_a.setText("W/A: " + item.getW_a());
                                   ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\UpdateDeleteFragment.java:330: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        holder.textViewPaymentStatus.setText("Payment Status: " + item.getPaymentStatus());
                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\UpdateDeleteFragment.java:330: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
        holder.textViewPaymentStatus.setText("Payment Status: " + item.getPaymentStatus());
                                             ~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\UpdateDeleteFragment.java:332: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
        holder.textTimeDate.setText("Time and Date:\n" + item.getCurrentDateTime());
                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\VarifyActivity.java:51: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                    textView.setText("Authentication error: " + errString);
                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\VarifyActivity.java:51: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                    textView.setText("Authentication error: " + errString);
                                     ~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\VarifyActivity.java:67: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                    textView.setText("Authentication failed.");
                                     ~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\VarifyActivity.java:80: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            textView.setText("Biometric authentication is not available on this device.");
                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\java\com\official\invoicegenarator\VarifyActivity.java:82: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            textView.setText("Biometric authentication is currently unavailable.");
                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "SetTextI18n":
   When calling TextView#setText
   * Never call Number#toString() to format numbers; it will not handle
   fraction separators and locale-specific digits properly. Consider using
   String#format with proper format specifications (%d or %f) instead.
   * Do not pass a string literal (e.g. "Hello") to display text. Hardcoded
   text can not be properly translated to other languages. Consider using
   Android resource strings instead.
   * Do not build messages by concatenating text chunks. Such messages can not
   be properly translated.

   https://developer.android.com/guide/topics/resources/localization.html

C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_download_list.xml:25: Warning: Hardcoded string "নতৃুন আপডেট", should use @string resource [HardcodedText]
                android:text="নতৃুন আপডেট"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_download_list.xml:32: Warning: Hardcoded string "পুরাতন আপডেট", should use @string resource [HardcodedText]
                android:text="পুরাতন আপডেট"/>
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_download_list.xml:44: Warning: Hardcoded string "Search PDFs", should use @string resource [HardcodedText]
        android:hint="Search PDFs"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_varify.xml:28: Warning: Hardcoded string "Click Icon for Biometric Authentication", should use @string resource [HardcodedText]
        android:text="Click Icon for Biometric Authentication"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\banklay.xml:20: Warning: Hardcoded string "Banking Account Details", should use @string resource [HardcodedText]
        android:text="Banking Account Details" />
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\banklay.xml:41: Warning: Hardcoded string "Bank Name", should use @string resource [HardcodedText]
                android:text="Bank Name"
                ~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\banklay.xml:55: Warning: Hardcoded string "Bank Muscat", should use @string resource [HardcodedText]
                android:text="Bank Muscat"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\banklay.xml:72: Warning: Hardcoded string "Account No", should use @string resource [HardcodedText]
                android:text="Account No"
                ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\banklay.xml:86: Warning: Hardcoded string "****************", should use @string resource [HardcodedText]
                android:text="****************"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\banklay.xml:104: Warning: Hardcoded string "Account name holder", should use @string resource [HardcodedText]
                android:text="Account name holder"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\banklay.xml:119: Warning: Hardcoded string "NABRAS AL KHOUDH TRAD & CON. PROJECT", should use @string resource [HardcodedText]
                android:text="NABRAS AL KHOUDH TRAD &amp; CON. PROJECT"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:107: Warning: Hardcoded string "VAT No: OM110000378X", should use @string resource [HardcodedText]
                android:text="VAT No: OM110000378X"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:138: Warning: Hardcoded string "Date :.........", should use @string resource [HardcodedText]
                android:text="Date :........."
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:156: Warning: Hardcoded string "QR No : QR 210724SIB", should use @string resource [HardcodedText]
                android:text="QR No : QR 210724SIB"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:175: Warning: Hardcoded string "Location : AKO Branch", should use @string resource [HardcodedText]
                android:text="Location : AKO Branch"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:194: Warning: Hardcoded string "VAT IN : OM1100198193", should use @string resource [HardcodedText]
                android:text="VAT IN : OM1100198193"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:231: Warning: Hardcoded string "Enter text here", should use @string resource [HardcodedText]
                    android:hint="Enter text here"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:232: Warning: Hardcoded string "SUBJECT:   ", should use @string resource [HardcodedText]
                    android:text="SUBJECT:   "
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:107: Warning: Hardcoded string "VAT No: OM110000378X", should use @string resource [HardcodedText]
                android:text="VAT No: OM110000378X"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:138: Warning: Hardcoded string "Date :.........", should use @string resource [HardcodedText]
                android:text="Date :........."
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:156: Warning: Hardcoded string "QR No : QR 210724SIB", should use @string resource [HardcodedText]
                android:text="QR No : QR 210724SIB"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:175: Warning: Hardcoded string "Location : AKO Branch", should use @string resource [HardcodedText]
                android:text="Location : AKO Branch"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:194: Warning: Hardcoded string "VAT IN : OM1100198193", should use @string resource [HardcodedText]
                android:text="VAT IN : OM1100198193"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:231: Warning: Hardcoded string "Enter text here", should use @string resource [HardcodedText]
                    android:hint="Enter text here"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:232: Warning: Hardcoded string "SUBJECT:   ", should use @string resource [HardcodedText]
                    android:text="SUBJECT:   "
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\custom_file_name_dialog.xml:18: Warning: Hardcoded string "Enter file name", should use @string resource [HardcodedText]
        android:text="Enter file name"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\custom_file_name_dialog.xml:26: Warning: Hardcoded string "Enter File Name", should use @string resource [HardcodedText]
        android:hint="Enter File Name"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\custom_file_name_dialog.xml:55: Warning: Hardcoded string "Cancel", should use @string resource [HardcodedText]
            android:text="Cancel"
            ~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\custom_file_name_dialog.xml:65: Warning: Hardcoded string "Save", should use @string resource [HardcodedText]
            android:text="Save"
            ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\dialog_add_expense.xml:12: Warning: Hardcoded string "Amount", should use @string resource [HardcodedText]
        android:hint="Amount"
        ~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\dialog_add_expense.xml:19: Warning: Hardcoded string "Category", should use @string resource [HardcodedText]
        android:hint="Category" />
        ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\dialog_add_expense.xml:25: Warning: Hardcoded string "Date", should use @string resource [HardcodedText]
        android:hint="Date"
        ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\dialog_add_expense.xml:33: Warning: Hardcoded string "Note", should use @string resource [HardcodedText]
        android:hint="Note"
        ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\dialog_add_expense.xml:40: Warning: Hardcoded string "Pick Date", should use @string resource [HardcodedText]
        android:text="Pick Date" />
        ~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\dialog_add_income.xml:13: Warning: Hardcoded string "Amount", should use @string resource [HardcodedText]
        android:hint="Amount"
        ~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\dialog_add_income.xml:23: Warning: Hardcoded string "Note", should use @string resource [HardcodedText]
        android:hint="Note"
        ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\dialog_add_income.xml:40: Warning: Hardcoded string "Date", should use @string resource [HardcodedText]
        android:text="Date"
        ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\dialog_add_income.xml:48: Warning: Hardcoded string "Save", should use @string resource [HardcodedText]
        android:text="Save"
        ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\dialog_exit_confirmation.xml:24: Warning: Hardcoded string "Are you sure you want to close First Invoice?", should use @string resource [HardcodedText]
            android:text="Are you sure you want to close First Invoice?"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\dialog_exit_confirmation.xml:35: Warning: Hardcoded string "Don't show this again", should use @string resource [HardcodedText]
            android:text="Don't show this again"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\dialog_exit_confirmation.xml:52: Warning: Hardcoded string "Cancel", should use @string resource [HardcodedText]
                android:text="Cancel" />
                ~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\dialog_exit_confirmation.xml:59: Warning: Hardcoded string "Yes", should use @string resource [HardcodedText]
                android:text="Yes" />
                ~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\dialog_number_to_words.xml:12: Warning: Hardcoded string "Number to Text Converter:", should use @string resource [HardcodedText]
        android:text="Number to Text Converter:"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\dialog_number_to_words.xml:31: Warning: Hardcoded string "Enter number", should use @string resource [HardcodedText]
            android:hint="Enter number"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\dialog_number_to_words.xml:53: Warning: Hardcoded string "Copy", should use @string resource [HardcodedText]
        android:text="Copy"
        ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\dialog_progress.xml:20: Warning: Hardcoded string "Downloading PDF...", should use @string resource [HardcodedText]
        android:text="Downloading PDF..."
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\dialog_save_pdf.xml:12: Warning: Hardcoded string "Enter file name (without .pdf)", should use @string resource [HardcodedText]
        android:hint="Enter file name (without .pdf)"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\dialog_update_item.xml:17: Warning: Hardcoded string "Description", should use @string resource [HardcodedText]
            android:hint="Description">
            ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\dialog_update_item.xml:31: Warning: Hardcoded string "Location", should use @string resource [HardcodedText]
            android:hint="Location">
            ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\dialog_update_item.xml:45: Warning: Hardcoded string "QR Code", should use @string resource [HardcodedText]
            android:hint="QR Code">
            ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\dialog_update_item.xml:59: Warning: Hardcoded string "LPO", should use @string resource [HardcodedText]
            android:hint="LPO">
            ~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\dialog_update_item.xml:73: Warning: Hardcoded string "Invoice Number", should use @string resource [HardcodedText]
            android:hint="Invoice Number">
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\dialog_update_item.xml:87: Warning: Hardcoded string "Amount", should use @string resource [HardcodedText]
            android:hint="Amount">
            ~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\dialog_update_item.xml:101: Warning: Hardcoded string "W/A", should use @string resource [HardcodedText]
            android:hint="W/A">
            ~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\dialog_update_item.xml:115: Warning: Hardcoded string "Payment Status", should use @string resource [HardcodedText]
            android:hint="Payment Status">
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\fgdata_upload.xml:31: Warning: Hardcoded string "Invoice Tracker", should use @string resource [HardcodedText]
            android:text="Invoice Tracker"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\fgdata_upload.xml:50: Warning: Hardcoded string "Description", should use @string resource [HardcodedText]
                    android:hint="Description">
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\fgdata_upload.xml:59: Warning: Hardcoded string "Description", should use @string resource [HardcodedText]
                        android:hint="Description" />
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\fgdata_upload.xml:65: Warning: Hardcoded string "Location", should use @string resource [HardcodedText]
                    android:hint="Location">
                    ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\fgdata_upload.xml:74: Warning: Hardcoded string "Location", should use @string resource [HardcodedText]
                        android:hint="Location" />
                        ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\fgdata_upload.xml:80: Warning: Hardcoded string "QR", should use @string resource [HardcodedText]
                    android:hint="QR">
                    ~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\fgdata_upload.xml:89: Warning: Hardcoded string "QR", should use @string resource [HardcodedText]
                        android:hint="QR" />
                        ~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\fgdata_upload.xml:95: Warning: Hardcoded string "LPO", should use @string resource [HardcodedText]
                    android:hint="LPO">
                    ~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\fgdata_upload.xml:104: Warning: Hardcoded string "LPO", should use @string resource [HardcodedText]
                        android:hint="LPO" />
                        ~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\fgdata_upload.xml:110: Warning: Hardcoded string "INV", should use @string resource [HardcodedText]
                    android:hint="INV">
                    ~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\fgdata_upload.xml:119: Warning: Hardcoded string "INV", should use @string resource [HardcodedText]
                        android:hint="INV" />
                        ~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\fgdata_upload.xml:125: Warning: Hardcoded string "Amount", should use @string resource [HardcodedText]
                    android:hint="Amount">
                    ~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\fgdata_upload.xml:134: Warning: Hardcoded string "Amount", should use @string resource [HardcodedText]
                        android:hint="Amount" />
                        ~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\fgdata_upload.xml:140: Warning: Hardcoded string "W/A", should use @string resource [HardcodedText]
                    android:hint="W/A">
                    ~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\fgdata_upload.xml:149: Warning: Hardcoded string "W/A", should use @string resource [HardcodedText]
                        android:hint="W/A" />
                        ~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\fgdata_upload.xml:155: Warning: Hardcoded string "Payment Status", should use @string resource [HardcodedText]
                    android:hint="Payment Status">
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\fgdata_upload.xml:164: Warning: Hardcoded string "Payment Status", should use @string resource [HardcodedText]
                        android:hint="Payment Status" />
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\fgdata_upload.xml:174: Warning: Hardcoded string "Upload Data", should use @string resource [HardcodedText]
                    android:text="Upload Data" />
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\footer_main.xml:15: Warning: Hardcoded string "Total amount RO 300 /- (Rial Omani Three Hundred rial-Only)", should use @string resource [HardcodedText]
        android:text="Total amount RO 300 /- (Rial Omani Three Hundred rial-Only)"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\fragment_data_upload.xml:14: Warning: Hardcoded string "Invoice Traker", should use @string resource [HardcodedText]
        android:text="Invoice Traker"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\fragment_data_upload.xml:35: Warning: Hardcoded string "Description", should use @string resource [HardcodedText]
                android:hint="Description">
                ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\fragment_data_upload.xml:44: Warning: Hardcoded string "Description", should use @string resource [HardcodedText]
                    android:hint="Description" />
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\fragment_data_upload.xml:50: Warning: Hardcoded string "Location", should use @string resource [HardcodedText]
                android:hint="Location">
                ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\fragment_data_upload.xml:59: Warning: Hardcoded string "Location", should use @string resource [HardcodedText]
                    android:hint="Location" />
                    ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\fragment_data_upload.xml:65: Warning: Hardcoded string "QR", should use @string resource [HardcodedText]
                android:hint="QR">
                ~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\fragment_data_upload.xml:74: Warning: Hardcoded string "QR", should use @string resource [HardcodedText]
                    android:hint="QR" />
                    ~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\fragment_data_upload.xml:80: Warning: Hardcoded string "LPO", should use @string resource [HardcodedText]
                android:hint="LPO">
                ~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\fragment_data_upload.xml:89: Warning: Hardcoded string "LPO", should use @string resource [HardcodedText]
                    android:hint="LPO" />
                    ~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\fragment_data_upload.xml:95: Warning: Hardcoded string "INV", should use @string resource [HardcodedText]
                android:hint="INV">
                ~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\fragment_data_upload.xml:104: Warning: Hardcoded string "INV", should use @string resource [HardcodedText]
                    android:hint="INV" />
                    ~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\fragment_data_upload.xml:110: Warning: Hardcoded string "Amount", should use @string resource [HardcodedText]
                android:hint="Amount">
                ~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\fragment_data_upload.xml:119: Warning: Hardcoded string "Amount", should use @string resource [HardcodedText]
                    android:hint="Amount" />
                    ~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\fragment_data_upload.xml:125: Warning: Hardcoded string "W/A", should use @string resource [HardcodedText]
                android:hint="W/A">
                ~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\fragment_data_upload.xml:134: Warning: Hardcoded string "W/A", should use @string resource [HardcodedText]
                    android:hint="W/A" />
                    ~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\fragment_data_upload.xml:139: Warning: Hardcoded string "Payment Status", should use @string resource [HardcodedText]
                android:hint="Payment Status">
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\fragment_data_upload.xml:148: Warning: Hardcoded string "Payment Status", should use @string resource [HardcodedText]
                    android:hint="Payment Status" />
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\fragment_data_upload.xml:155: Warning: Hardcoded string "Upload Data", should use @string resource [HardcodedText]
                android:text="Upload Data" />
                ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\fragment_expense.xml:32: Warning: Hardcoded string "Add Expense", should use @string resource [HardcodedText]
        android:text="Add Expense"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\fragment_update_delete.xml:27: Warning: Hardcoded string "Search Invoice Track...", should use @string resource [HardcodedText]
        android:hint="Search Invoice Track..."
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\header_main.xml:17: Warning: Hardcoded string "مشاريع نبراس الخوض للتجارة والمقاوالت", should use @string resource [HardcodedText]
            android:text="مشاريع نبراس الخوض للتجارة والمقاوالت"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\header_main.xml:29: Warning: Hardcoded string "NABRAS AL- KHOUDH TRAD. & CONT.PROJECTS", should use @string resource [HardcodedText]
            android:text="NABRAS AL- KHOUDH TRAD. &amp; CONT.PROJECTS"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\header_main.xml:46: Warning: Hardcoded string "ص.ب:123,32الخوض ، سلطنة عمان، هاتف:99535325, س:1129470", should use @string resource [HardcodedText]
            android:text="ص.ب:123,32الخوض ، سلطنة عمان، هاتف:99535325, س:1129470"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\header_main.xml:57: Warning: Hardcoded string "P.O. BOX 32, 123Al - khoudh, sultanate of Oman, Tel:99535325 C.R. NO: 1129470", should use @string resource [HardcodedText]
            android:text="P.O. BOX 32, 123Al - khoudh, sultanate of Oman, Tel:99535325 C.R. NO: 1129470"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\header_main.xml:67: Warning: Hardcoded string "E-mail: <EMAIL>", should use @string resource [HardcodedText]
        android:text="E-mail: <EMAIL>"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\header_main.xml:77: Warning: Hardcoded string "QUOTATION", should use @string resource [HardcodedText]
        android:text="QUOTATION"
        ~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item_data_invoice_traker.xml:19: Warning: Hardcoded string "Date and Time", should use @string resource [HardcodedText]
        android:text="Date and Time"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item_data_invoice_traker.xml:34: Warning: Hardcoded string "Description", should use @string resource [HardcodedText]
        android:text="Description"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item_data_invoice_traker.xml:44: Warning: Hardcoded string "Location", should use @string resource [HardcodedText]
        android:text="Location"
        ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item_data_invoice_traker.xml:57: Warning: Hardcoded string "QR Code", should use @string resource [HardcodedText]
        android:text="QR Code"
        ~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item_data_invoice_traker.xml:70: Warning: Hardcoded string "LPO", should use @string resource [HardcodedText]
        android:text="LPO"
        ~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item_data_invoice_traker.xml:83: Warning: Hardcoded string "Invoice Number", should use @string resource [HardcodedText]
        android:text="Invoice Number"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item_data_invoice_traker.xml:96: Warning: Hardcoded string "Amount", should use @string resource [HardcodedText]
        android:text="Amount"
        ~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item_data_invoice_traker.xml:109: Warning: Hardcoded string "W/A", should use @string resource [HardcodedText]
        android:text="W/A"
        ~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item_data_invoice_traker.xml:122: Warning: Hardcoded string "Payment Status", should use @string resource [HardcodedText]
        android:text="Payment Status"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item_data_invoice_traker.xml:147: Warning: Hardcoded string "Edit", should use @string resource [HardcodedText]
            android:text="Edit" />
            ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item_data_invoice_traker.xml:156: Warning: Hardcoded string "Delete", should use @string resource [HardcodedText]
            android:text="Delete" />
            ~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item_expense.xml:12: Warning: Hardcoded string "Category", should use @string resource [HardcodedText]
        android:text="Category"
        ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\item_expense.xml:21: Warning: Hardcoded string "Amount", should use @string resource [HardcodedText]
        android:text="Amount"
        ~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\ivtraker_item_row.xml:13: Warning: Hardcoded string "Description", should use @string resource [HardcodedText]
        android:text="Description"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\ivtraker_item_row.xml:23: Warning: Hardcoded string "Location", should use @string resource [HardcodedText]
        android:text="Location"
        ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\ivtraker_item_row.xml:32: Warning: Hardcoded string "QR", should use @string resource [HardcodedText]
        android:text="QR"
        ~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\ivtraker_item_row.xml:41: Warning: Hardcoded string "LPO", should use @string resource [HardcodedText]
        android:text="LPO"
        ~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\ivtraker_item_row.xml:50: Warning: Hardcoded string "INB", should use @string resource [HardcodedText]
        android:text="INB"
        ~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\ivtraker_item_row.xml:59: Warning: Hardcoded string "Amount", should use @string resource [HardcodedText]
        android:text="Amount"
        ~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\ivtraker_item_row.xml:68: Warning: Hardcoded string "W/A", should use @string resource [HardcodedText]
        android:text="W/A"
        ~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\ivtraker_item_row.xml:84: Warning: Hardcoded string "Update", should use @string resource [HardcodedText]
            android:text="Update"
            ~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\ivtraker_item_row.xml:92: Warning: Hardcoded string "Delete", should use @string resource [HardcodedText]
            android:text="Delete"
            ~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\loading_dialog.xml:22: Warning: Hardcoded string "Loading...", should use @string resource [HardcodedText]
        android:text="Loading..."
        ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\menu\nav_menu.xml:4: Warning: Hardcoded string "Wallet Section", should use @string resource [HardcodedText]
    <item android:title="Wallet Section"
          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\menu\nav_menu.xml:13: Warning: Hardcoded string "Money Bag", should use @string resource [HardcodedText]
                android:title="Money Bag" />
                ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\menu\nav_menu.xml:17: Warning: Hardcoded string "Invoice Design Section", should use @string resource [HardcodedText]
    <item android:title="Invoice Design Section">
          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\menu\nav_menu.xml:23: Warning: Hardcoded string "Invoice Design", should use @string resource [HardcodedText]
                android:title="Invoice Design" />
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\menu\nav_menu.xml:27: Warning: Hardcoded string "Worker Attendance Section", should use @string resource [HardcodedText]
    <item android:title="Worker Attendance Section">
          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\menu\nav_menu.xml:33: Warning: Hardcoded string "Worker Attendance", should use @string resource [HardcodedText]
                android:title="Worker Attendance" />
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\menu\nav_menu.xml:37: Warning: Hardcoded string "Finger Print Section", should use @string resource [HardcodedText]
    <item android:title="Finger Print Section">
          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\menu\nav_menu.xml:43: Warning: Hardcoded string "Finger Print ", should use @string resource [HardcodedText]
                android:title="Finger Print " />
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\pdf_item.xml:30: Warning: Hardcoded string "PDF Name", should use @string resource [HardcodedText]
                android:text="PDF Name"
                ~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\pdf_item.xml:47: Warning: Hardcoded string "Time and date", should use @string resource [HardcodedText]
                    android:text="Time and date"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\pdf_item.xml:59: Warning: Hardcoded string "File Size", should use @string resource [HardcodedText]
                    android:text="File Size"
                    ~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table.xml:20: Warning: Hardcoded string "S.N", should use @string resource [HardcodedText]
            android:text="S.N"
            ~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table.xml:38: Warning: Hardcoded string "Description", should use @string resource [HardcodedText]
            android:text="Description"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table.xml:51: Warning: Hardcoded string "QTY", should use @string resource [HardcodedText]
            android:text="QTY"
            ~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table.xml:64: Warning: Hardcoded string "UOM", should use @string resource [HardcodedText]
            android:text="UOM"
            ~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table.xml:78: Warning: Hardcoded string "Amount", should use @string resource [HardcodedText]
            android:text="Amount"
            ~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table_two.xml:20: Warning: Hardcoded string "S.N", should use @string resource [HardcodedText]
            android:text="S.N"
            ~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table_two.xml:38: Warning: Hardcoded string "Description", should use @string resource [HardcodedText]
            android:text="Description"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table_two.xml:51: Warning: Hardcoded string "QTY", should use @string resource [HardcodedText]
            android:text="QTY"
            ~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table_two.xml:64: Warning: Hardcoded string "UOM", should use @string resource [HardcodedText]
            android:text="UOM"
            ~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\table_two.xml:78: Warning: Hardcoded string "Amount", should use @string resource [HardcodedText]
            android:text="Amount"
            ~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo.xml:37: Warning: Hardcoded string "Total Amount Excluding 5% VAT", should use @string resource [HardcodedText]
            android:text="Total Amount Excluding 5% VAT"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\texinfo_two.xml:37: Warning: Hardcoded string "Total Amount Excluding 5% VAT", should use @string resource [HardcodedText]
            android:text="Total Amount Excluding 5% VAT"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\menu\text_to_number.xml:7: Warning: Hardcoded string "Convert Number to Words", should use @string resource [HardcodedText]
        android:title="Convert Number to Words"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\menu\text_to_number.xml:12: Warning: Hardcoded string "INVOICE TRACKER", should use @string resource [HardcodedText]
        android:title="INVOICE TRACKER"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "HardcodedText":
   Hardcoding text attributes directly in layout files is bad for several
   reasons:

   * When creating configuration variations (for example for landscape or
   portrait) you have to repeat the actual text (and keep it up to date when
   making changes)

   * The application cannot be translated to other languages by just adding
   new translations for existing string resources.

   There are quickfixes to automatically extract this hardcoded string into a
   resource lookup.

C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_download_list.xml:65: Warning: When you define paddingEnd you should probably also define paddingStart for right-to-left symmetry [RtlSymmetry]
        android:paddingEnd="4dp"
        ~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:16: Warning: When you define paddingLeft you should probably also define paddingRight for right-to-left symmetry [RtlSymmetry]
        android:paddingLeft="6dp"
        ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:122: Warning: When you define paddingLeft you should probably also define paddingRight for right-to-left symmetry [RtlSymmetry]
        android:paddingLeft="6dp"
        ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:16: Warning: When you define paddingLeft you should probably also define paddingRight for right-to-left symmetry [RtlSymmetry]
        android:paddingLeft="6dp"
        ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:122: Warning: When you define paddingLeft you should probably also define paddingRight for right-to-left symmetry [RtlSymmetry]
        android:paddingLeft="6dp"
        ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\dialog_save_pdf.xml:23: Warning: When you define paddingLeft you should probably also define paddingRight for right-to-left symmetry [RtlSymmetry]
            android:paddingLeft="16dp"
            ~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "RtlSymmetry":
   If you specify padding or margin on the left side of a layout, you should
   probably also specify padding on the right side (and vice versa) for
   right-to-left layout symmetry.

C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_selection.xml:47: Warning: Consider replacing android:layout_marginRight with android:layout_marginEnd="2dp" to better support right-to-left layouts [RtlHardcoded]
                    android:layout_marginRight="2dp"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_selection.xml:68: Warning: Consider replacing android:layout_marginLeft with android:layout_marginStart="2dp" to better support right-to-left layouts [RtlHardcoded]
                    android:layout_marginLeft="2dp"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\activity_selection.xml:96: Warning: Consider replacing android:layout_marginRight with android:layout_marginEnd="3dp" to better support right-to-left layouts [RtlHardcoded]
                        android:layout_marginRight="3dp"
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:16: Warning: Consider replacing android:paddingLeft with android:paddingStart="6dp" to better support right-to-left layouts [RtlHardcoded]
        android:paddingLeft="6dp"
        ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:122: Warning: Consider replacing android:paddingLeft with android:paddingStart="6dp" to better support right-to-left layouts [RtlHardcoded]
        android:paddingLeft="6dp"
        ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main.xml:219: Warning: Use "start" instead of "left" to ensure correct behavior in right-to-left locales [RtlHardcoded]
            android:gravity="left">
                             ~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:16: Warning: Consider replacing android:paddingLeft with android:paddingStart="6dp" to better support right-to-left layouts [RtlHardcoded]
        android:paddingLeft="6dp"
        ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:122: Warning: Consider replacing android:paddingLeft with android:paddingStart="6dp" to better support right-to-left layouts [RtlHardcoded]
        android:paddingLeft="6dp"
        ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\content_main_two.xml:219: Warning: Use "start" instead of "left" to ensure correct behavior in right-to-left locales [RtlHardcoded]
            android:gravity="left">
                             ~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\dialog_save_pdf.xml:23: Warning: Consider replacing android:paddingLeft with android:paddingStart="16dp" to better support right-to-left layouts [RtlHardcoded]
            android:paddingLeft="16dp"
            ~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\footer_main.xml:23: Warning: Consider replacing android:layout_alignLeft with android:layout_alignStart="@id/edit_text" to better support right-to-left layouts [RtlHardcoded]
        android:layout_alignLeft="@id/edit_text"
        ~~~~~~~~~~~~~~~~~~~~~~~~
C:\xampp\htdocs\MtcInvoiceMasudvi\MtcInvoice\app\src\main\res\layout\footer_main.xml:24: Warning: Consider replacing android:layout_alignRight with android:layout_alignEnd="@id/edit_text" to better support right-to-left layouts [RtlHardcoded]
        android:layout_alignRight="@id/edit_text"
        ~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "RtlHardcoded":
   Using Gravity#LEFT and Gravity#RIGHT can lead to problems when a layout is
   rendered in locales where text flows from right to left. Use Gravity#START
   and Gravity#END instead. Similarly, in XML gravity and layout_gravity
   attributes, use start rather than left.

   For XML attributes such as paddingLeft and layout_marginLeft, use
   paddingStart and layout_marginStart. NOTE: If your minSdkVersion is less
   than 17, you should add both the older left/right attributes as well as the
   new start/end attributes. On older platforms, where RTL is not supported
   and the start/end attributes are unknown and therefore ignored, you need
   the older left/right attributes. There is a separate lint check which
   catches that type of error.

   (Note: For Gravity#LEFT and Gravity#START, you can use these constants even
   when targeting older platforms, because the start bitmask is a superset of
   the left bitmask. Therefore, you can use gravity="start" rather than
   gravity="left|start".)

52 errors, 728 warnings
