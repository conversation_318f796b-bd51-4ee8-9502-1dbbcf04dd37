<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Number to Text Converter:"
        android:layout_gravity="center"
        android:gravity="center"
        android:background="@drawable/rounded_corner"
        android:padding="5dp"
        android:layout_margin="5dp"
        android:textStyle="bold"
        android:textColor="@color/white"
        android:textSize="18sp" />

    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/editTextNumber"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="numberDecimal"
            android:hint="Enter number"
            android:padding="8dp" />
    </com.google.android.material.textfield.TextInputLayout>

    <TextView
        android:id="@+id/textViewWords"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/tround"
        android:layout_marginTop="5dp"
        android:layout_marginBottom="5dp"
        android:padding="25dp"
        android:textColor="@color/white"
        android:textSize="12sp"
       />

    <Button
        android:id="@+id/buttonCopy"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:layout_gravity="center"
        android:text="Copy"
        android:layout_marginTop="6dp" />

</LinearLayout>
