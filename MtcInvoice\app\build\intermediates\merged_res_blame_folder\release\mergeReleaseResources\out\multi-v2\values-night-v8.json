{"logs": [{"outputFile": "com.official.invoicegenarator.app-mergeReleaseResources-42:/values-night-v8/values-night-v8.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\43df220cec1a56a3604bd592dc15e9f7\\transformed\\appcompat-1.7.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}, "to": {"startLines": "15,16,17,18,19,20,21,48", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "744,814,898,982,1078,1180,1282,4264", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "809,893,977,1073,1175,1277,1371,4348"}}, {"source": "C:\\xampp\\htdocs\\MtcInvoiceMasudvi\\MtcInvoice\\app\\src\\main\\res\\values-night\\themes.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "100", "endLines": "14", "endColumns": "12", "endOffsets": "827"}, "to": {"startLines": "2", "startColumns": "4", "startOffsets": "100", "endLines": "14", "endColumns": "12", "endOffsets": "739"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\be09ca956cfff01cb3b7495f5bc45a31\\transformed\\material-1.12.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,241,330,431,538,645,744,851,954,1081,1169,1293,1395,1497,1613,1715,1829,1957,2073,2195,2331,2451,2585,2705,2817,2943,3060,3184,3314,3436,3574,3708,3824", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "125,236,325,426,533,640,739,846,949,1076,1164,1288,1390,1492,1608,1710,1824,1952,2068,2190,2326,2446,2580,2700,2812,2938,3055,3179,3309,3431,3569,3703,3819,3939"}, "to": {"startLines": "22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1376,1451,1562,1651,1752,1859,1966,2065,2172,2275,2402,2490,2614,2716,2818,2934,3036,3150,3278,3394,3516,3652,3772,3906,4026,4138,4353,4470,4594,4724,4846,4984,5118,5234", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "1446,1557,1646,1747,1854,1961,2060,2167,2270,2397,2485,2609,2711,2813,2929,3031,3145,3273,3389,3511,3647,3767,3901,4021,4133,4259,4465,4589,4719,4841,4979,5113,5229,5349"}}]}]}