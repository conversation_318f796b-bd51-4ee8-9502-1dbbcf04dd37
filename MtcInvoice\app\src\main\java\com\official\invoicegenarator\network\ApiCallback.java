package com.official.invoicegenarator.network;

/**
 * Generic callback interface for API operations
 * 
 * @param <T> The type of data returned on success
 */
public interface ApiCallback<T> {
    
    /**
     * Called when the API request is successful
     * 
     * @param result The result data
     */
    void onSuccess(T result);
    
    /**
     * Called when the API request fails
     * 
     * @param error Error message describing the failure
     */
    void onError(String error);
}
